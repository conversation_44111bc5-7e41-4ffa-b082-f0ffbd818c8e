import 'package:flutter/material.dart';

import '../../theme/app_colors.dart';

/// An enhanced empty state component that provides consistent empty state
/// displays across the application with customizable content and actions.
/// 
/// This component eliminates duplication of empty state implementations
/// and provides a standardized user experience.
class AppEmptyStateEnhanced extends StatelessWidget {
  /// The icon to display
  final IconData icon;
  
  /// The main title text
  final String title;
  
  /// The subtitle/description text
  final String subtitle;
  
  /// Primary action button text
  final String? primaryActionText;
  
  /// Primary action callback
  final VoidCallback? onPrimaryAction;
  
  /// Secondary action button text
  final String? secondaryActionText;
  
  /// Secondary action callback
  final VoidCallback? onSecondaryAction;
  
  /// Custom icon color
  final Color? iconColor;
  
  /// Custom background color
  final Color? backgroundColor;
  
  /// Whether to show the refresh hint
  final bool showRefreshHint;
  
  /// Custom refresh hint text
  final String? refreshHintText;
  
  /// Whether to use compact layout
  final bool compact;
  
  /// Custom padding
  final EdgeInsets? padding;
  
  /// Custom illustration widget (overrides icon)
  final Widget? illustration;
  
  const AppEmptyStateEnhanced({
    super.key,
    required this.icon,
    required this.title,
    required this.subtitle,
    this.primaryActionText,
    this.onPrimaryAction,
    this.secondaryActionText,
    this.onSecondaryAction,
    this.iconColor,
    this.backgroundColor,
    this.showRefreshHint = true,
    this.refreshHintText,
    this.compact = false,
    this.padding,
    this.illustration,
  });
  
  /// Factory constructor for no data states
  factory AppEmptyStateEnhanced.noData({
    required String entityName,
    required VoidCallback onAdd,
    IconData? icon,
    Color? iconColor,
    bool showRefreshHint = true,
  }) {
    return AppEmptyStateEnhanced(
      icon: icon ?? Icons.inbox_outlined,
      title: 'No $entityName Found',
      subtitle: 'Add your first $entityName by tapping the button below',
      primaryActionText: 'Add $entityName',
      onPrimaryAction: onAdd,
      iconColor: iconColor ?? AppColors.primary,
      showRefreshHint: showRefreshHint,
    );
  }
  
  /// Factory constructor for search results
  factory AppEmptyStateEnhanced.noSearchResults({
    required String searchTerm,
    VoidCallback? onClearSearch,
    bool showRefreshHint = false,
  }) {
    return AppEmptyStateEnhanced(
      icon: Icons.search_off,
      title: 'No Results Found',
      subtitle: 'No results found for "$searchTerm". Try adjusting your search.',
      primaryActionText: onClearSearch != null ? 'Clear Search' : null,
      onPrimaryAction: onClearSearch,
      iconColor: Colors.grey[600],
      showRefreshHint: showRefreshHint,
    );
  }
  
  /// Factory constructor for error states
  factory AppEmptyStateEnhanced.error({
    required String message,
    required VoidCallback onRetry,
    bool showRefreshHint = false,
  }) {
    return AppEmptyStateEnhanced(
      icon: Icons.error_outline,
      title: 'Something went wrong',
      subtitle: message,
      primaryActionText: 'Try Again',
      onPrimaryAction: onRetry,
      iconColor: Colors.red[400],
      showRefreshHint: showRefreshHint,
    );
  }
  
  /// Factory constructor for loading states
  factory AppEmptyStateEnhanced.loading({
    String title = 'Loading...',
    String subtitle = 'Please wait while we fetch your data',
  }) {
    return AppEmptyStateEnhanced(
      icon: Icons.hourglass_empty,
      title: title,
      subtitle: subtitle,
      showRefreshHint: false,
      illustration: const CircularProgressIndicator(),
    );
  }
  
  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final effectivePadding = padding ?? EdgeInsets.symmetric(
      horizontal: compact ? 24 : 32,
      vertical: compact ? 16 : 24,
    );
    
    return Center(
      child: SingleChildScrollView(
        child: Card(
          margin: effectivePadding,
          elevation: 0,
          color: backgroundColor,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
            side: BorderSide(
              color: Colors.grey.withAlpha(30),
              width: 1,
            ),
          ),
          child: Padding(
            padding: EdgeInsets.all(compact ? 20 : 24),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // Icon or illustration
                _buildIconOrIllustration(context),
                
                SizedBox(height: compact ? 16 : 20),
                
                // Title
                Text(
                  title,
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                    fontSize: compact ? 16 : null,
                  ),
                  textAlign: TextAlign.center,
                ),
                
                SizedBox(height: compact ? 6 : 8),
                
                // Subtitle
                Text(
                  subtitle,
                  style: theme.textTheme.bodyMedium?.copyWith(
                    color: Colors.grey[600],
                    fontSize: compact ? 13 : null,
                  ),
                  textAlign: TextAlign.center,
                ),
                
                SizedBox(height: compact ? 16 : 20),
                
                // Action buttons
                _buildActionButtons(context),
                
                // Refresh hint
                if (showRefreshHint) ...[
                  SizedBox(height: compact ? 8 : 12),
                  _buildRefreshHint(context),
                ],
              ],
            ),
          ),
        ),
      ),
    );
  }
  
  Widget _buildIconOrIllustration(BuildContext context) {
    if (illustration != null) {
      return SizedBox(
        width: compact ? 40 : 48,
        height: compact ? 40 : 48,
        child: illustration!,
      );
    }
    
    return Icon(
      icon,
      size: compact ? 40 : 48,
      color: iconColor ?? AppColors.primary,
    );
  }
  
  Widget _buildActionButtons(BuildContext context) {
    final buttons = <Widget>[];
    
    // Primary action button
    if (primaryActionText != null && onPrimaryAction != null) {
      buttons.add(
        ElevatedButton.icon(
          onPressed: onPrimaryAction,
          icon: Icon(Icons.add, size: compact ? 14 : 16),
          label: Text(primaryActionText!),
          style: ElevatedButton.styleFrom(
            backgroundColor: AppColors.primary,
            foregroundColor: Colors.white,
            padding: EdgeInsets.symmetric(
              horizontal: compact ? 12 : 16,
              vertical: compact ? 8 : 10,
            ),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
          ),
        ),
      );
    }
    
    // Secondary action button
    if (secondaryActionText != null && onSecondaryAction != null) {
      if (buttons.isNotEmpty) {
        buttons.add(SizedBox(width: compact ? 8 : 12));
      }
      
      buttons.add(
        OutlinedButton(
          onPressed: onSecondaryAction,
          style: OutlinedButton.styleFrom(
            padding: EdgeInsets.symmetric(
              horizontal: compact ? 12 : 16,
              vertical: compact ? 8 : 10,
            ),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
          ),
          child: Text(secondaryActionText!),
        ),
      );
    }
    
    if (buttons.isEmpty) return const SizedBox.shrink();
    
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: buttons,
    );
  }
  
  Widget _buildRefreshHint(BuildContext context) {
    final theme = Theme.of(context);
    final hintText = refreshHintText ?? 'Pull down to refresh';
    
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Icon(
          Icons.refresh,
          size: compact ? 12 : 14,
          color: AppColors.textSecondary,
        ),
        const SizedBox(width: 4),
        Text(
          hintText,
          style: theme.textTheme.bodySmall?.copyWith(
            color: AppColors.textSecondary,
            fontSize: compact ? 11 : null,
          ),
        ),
      ],
    );
  }
}

/// Specialized empty state components for common use cases

/// Empty state for income records
class IncomeEmptyState extends AppEmptyStateEnhanced {
  const IncomeEmptyState({
    super.key,
    required VoidCallback onAdd,
  }) : super(
    icon: Icons.account_balance_wallet_outlined,
    title: 'No Income Records Found',
    subtitle: 'Add your first income record by tapping the Add button',
    primaryActionText: 'Add Income Record',
    onPrimaryAction: onAdd,
    iconColor: AppColors.success,
  );
}

/// Empty state for order records
class OrdersEmptyState extends AppEmptyStateEnhanced {
  const OrdersEmptyState({
    super.key,
    required VoidCallback onAdd,
  }) : super(
    icon: Icons.assignment_outlined,
    title: 'No Orders Found',
    subtitle: 'Add your first order record by tapping the Add button',
    primaryActionText: 'Add Order Record',
    onPrimaryAction: onAdd,
    iconColor: Colors.blue,
  );
}

/// Empty state for performance records
class PerformanceEmptyState extends AppEmptyStateEnhanced {
  const PerformanceEmptyState({
    super.key,
    required VoidCallback onAdd,
  }) : super(
    icon: Icons.insights_outlined,
    title: 'No Performance Records Found',
    subtitle: 'Add your first performance record by tapping the Add button',
    primaryActionText: 'Add Performance Record',
    onPrimaryAction: onAdd,
    iconColor: Colors.purple,
  );
}

/// Empty state for spare parts
class SparePartsEmptyState extends AppEmptyStateEnhanced {
  const SparePartsEmptyState({
    super.key,
    required VoidCallback onAdd,
  }) : super(
    icon: Icons.build_outlined,
    title: 'No Spare Parts Found',
    subtitle: 'Add your first spare part by tapping the Add button',
    primaryActionText: 'Add Spare Part',
    onPrimaryAction: onAdd,
    iconColor: Colors.orange,
  );
}
