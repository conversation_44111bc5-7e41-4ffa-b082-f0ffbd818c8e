import 'package:flutter/material.dart';

import '../../theme/app_colors.dart';
import '../../utils/date_helper.dart';

/// A reusable date range selector component that provides consistent
/// date range selection functionality across the application.
/// 
/// This component eliminates the duplication of date range selector
/// implementations across multiple screens and provides a standardized
/// user experience.
class AppDateRangeSelector extends StatelessWidget {
  /// The current date range
  final DateTimeRange dateRange;
  
  /// Callback when a new date range is selected
  final ValueChanged<DateTimeRange> onDateRangeSelected;
  
  /// Whether the selector is enabled
  final bool enabled;
  
  /// Custom label text (optional)
  final String? label;
  
  /// Whether to show the label
  final bool showLabel;
  
  /// Custom background color
  final Color? backgroundColor;
  
  /// Custom text color
  final Color? textColor;
  
  /// Custom border radius
  final double borderRadius;
  
  /// Custom padding
  final EdgeInsets? padding;
  
  /// Whether to use compact styling
  final bool compact;
  
  /// Custom icon
  final IconData? icon;
  
  /// Whether to show the icon
  final bool showIcon;
  
  const AppDateRangeSelector({
    super.key,
    required this.dateRange,
    required this.onDateRangeSelected,
    this.enabled = true,
    this.label,
    this.showLabel = false,
    this.backgroundColor,
    this.textColor,
    this.borderRadius = 8.0,
    this.padding,
    this.compact = false,
    this.icon,
    this.showIcon = true,
  });
  
  /// Factory constructor for app bar usage (common pattern)
  factory AppDateRangeSelector.forAppBar({
    required DateTimeRange dateRange,
    required ValueChanged<DateTimeRange> onDateRangeSelected,
    bool enabled = true,
  }) {
    return AppDateRangeSelector(
      dateRange: dateRange,
      onDateRangeSelected: onDateRangeSelected,
      enabled: enabled,
      backgroundColor: Colors.white.withAlpha(30),
      textColor: Colors.white,
      compact: true,
    );
  }
  
  /// Factory constructor for form usage
  factory AppDateRangeSelector.forForm({
    required DateTimeRange dateRange,
    required ValueChanged<DateTimeRange> onDateRangeSelected,
    String? label,
    bool enabled = true,
  }) {
    return AppDateRangeSelector(
      dateRange: dateRange,
      onDateRangeSelected: onDateRangeSelected,
      enabled: enabled,
      label: label,
      showLabel: true,
      backgroundColor: Colors.grey.withAlpha(20),
    );
  }
  
  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final effectiveBackgroundColor = backgroundColor ?? 
        theme.colorScheme.surface.withAlpha(50);
    final effectiveTextColor = textColor ?? theme.colorScheme.onSurface;
    final effectivePadding = padding ?? EdgeInsets.symmetric(
      horizontal: compact ? 12 : 16,
      vertical: compact ? 8 : 12,
    );
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        if (showLabel && label != null) ...[
          Padding(
            padding: const EdgeInsets.only(bottom: 8),
            child: Text(
              label!,
              style: theme.textTheme.labelMedium?.copyWith(
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
        Material(
          color: Colors.transparent,
          child: InkWell(
            onTap: enabled ? () => _showDateRangePicker(context) : null,
            borderRadius: BorderRadius.circular(borderRadius),
            child: Container(
              padding: effectivePadding,
              decoration: BoxDecoration(
                color: effectiveBackgroundColor,
                borderRadius: BorderRadius.circular(borderRadius),
                border: Border.all(
                  color: theme.colorScheme.outline.withAlpha(50),
                  width: 1,
                ),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  if (showIcon) ...[
                    Icon(
                      icon ?? Icons.date_range,
                      color: effectiveTextColor.withAlpha(180),
                      size: compact ? 16 : 20,
                    ),
                    const SizedBox(width: 8),
                  ],
                  Expanded(
                    child: Text(
                      _formatDateRange(dateRange),
                      style: theme.textTheme.bodyMedium?.copyWith(
                        color: effectiveTextColor,
                        fontSize: compact ? 13 : null,
                        fontWeight: compact ? FontWeight.w500 : null,
                      ),
                    ),
                  ),
                  if (enabled) ...[
                    const SizedBox(width: 8),
                    Icon(
                      Icons.arrow_drop_down,
                      color: effectiveTextColor.withAlpha(180),
                      size: compact ? 16 : 20,
                    ),
                  ],
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }
  
  /// Show the date range picker dialog
  Future<void> _showDateRangePicker(BuildContext context) async {
    final picked = await showDateRangePicker(
      context: context,
      firstDate: DateTime(2020),
      lastDate: DateTime.now().add(const Duration(days: 365)),
      initialDateRange: dateRange,
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: Theme.of(context).colorScheme.copyWith(
              primary: AppColors.primary,
            ),
          ),
          child: child!,
        );
      },
    );
    
    if (picked != null && picked != dateRange) {
      onDateRangeSelected(picked);
    }
  }
  
  /// Format the date range for display
  String _formatDateRange(DateTimeRange range) {
    final startFormatted = DateHelper.formatForDisplay(range.start);
    final endFormatted = DateHelper.formatForDisplay(range.end);
    
    // If same month and year, show abbreviated format
    if (range.start.year == range.end.year && 
        range.start.month == range.end.month) {
      return '${range.start.day} - ${range.end.day} ${DateHelper.getMonthName(range.start.month)} ${range.start.year}';
    }
    
    return '$startFormatted - $endFormatted';
  }
}

/// A shimmer loading version of the date range selector
class AppDateRangeSelectorShimmer extends StatelessWidget {
  final bool compact;
  final double borderRadius;
  final EdgeInsets? padding;
  
  const AppDateRangeSelectorShimmer({
    super.key,
    this.compact = false,
    this.borderRadius = 8.0,
    this.padding,
  });
  
  @override
  Widget build(BuildContext context) {
    final effectivePadding = padding ?? EdgeInsets.symmetric(
      horizontal: compact ? 12 : 16,
      vertical: compact ? 8 : 12,
    );
    
    return Container(
      padding: effectivePadding,
      decoration: BoxDecoration(
        color: Colors.white.withAlpha(30),
        borderRadius: BorderRadius.circular(borderRadius),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            width: compact ? 16 : 20,
            height: compact ? 16 : 20,
            decoration: BoxDecoration(
              color: Colors.white.withAlpha(60),
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Container(
              height: compact ? 14 : 16,
              decoration: BoxDecoration(
                color: Colors.white.withAlpha(60),
                borderRadius: BorderRadius.circular(2),
              ),
            ),
          ),
          const SizedBox(width: 8),
          Container(
            width: compact ? 16 : 20,
            height: compact ? 16 : 20,
            decoration: BoxDecoration(
              color: Colors.white.withAlpha(60),
              borderRadius: BorderRadius.circular(2),
            ),
          ),
        ],
      ),
    );
  }
}

/// Quick preset date ranges for common use cases
class DateRangePresets {
  static DateTimeRange get thisMonth {
    final now = DateTime.now();
    return DateTimeRange(
      start: DateTime(now.year, now.month, 1),
      end: DateTime(now.year, now.month + 1, 0),
    );
  }
  
  static DateTimeRange get lastMonth {
    final now = DateTime.now();
    final lastMonth = DateTime(now.year, now.month - 1, 1);
    return DateTimeRange(
      start: lastMonth,
      end: DateTime(lastMonth.year, lastMonth.month + 1, 0),
    );
  }
  
  static DateTimeRange get thisWeek {
    final now = DateTime.now();
    final startOfWeek = now.subtract(Duration(days: now.weekday - 1));
    return DateTimeRange(
      start: DateTime(startOfWeek.year, startOfWeek.month, startOfWeek.day),
      end: DateTime(now.year, now.month, now.day),
    );
  }
  
  static DateTimeRange get lastWeek {
    final now = DateTime.now();
    final startOfLastWeek = now.subtract(Duration(days: now.weekday + 6));
    final endOfLastWeek = startOfLastWeek.add(const Duration(days: 6));
    return DateTimeRange(
      start: DateTime(startOfLastWeek.year, startOfLastWeek.month, startOfLastWeek.day),
      end: DateTime(endOfLastWeek.year, endOfLastWeek.month, endOfLastWeek.day),
    );
  }
  
  static DateTimeRange get last30Days {
    final now = DateTime.now();
    final start = now.subtract(const Duration(days: 30));
    return DateTimeRange(
      start: DateTime(start.year, start.month, start.day),
      end: DateTime(now.year, now.month, now.day),
    );
  }
}
