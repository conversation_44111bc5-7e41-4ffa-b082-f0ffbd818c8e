import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';

import '../../main.dart';
import '../utils/snackbar_utils.dart';
import 'exceptions.dart';
import 'failures.dart';

/// Unified error handling system that provides consistent error handling
/// across the entire application with user-friendly feedback and logging.
class UnifiedErrorHandler {
  static final UnifiedErrorHandler _instance = UnifiedErrorHandler._internal();
  factory UnifiedErrorHandler() => _instance;
  UnifiedErrorHandler._internal();
  
  /// Handle a failure with appropriate user feedback
  static void handleFailure(
    Failure failure, {
    bool showSnackbar = true,
    String? customMessage,
    BuildContext? context,
    VoidCallback? onRetry,
  }) {
    // Log the error for debugging
    _logError(failure);
    
    // Show user feedback if requested
    if (showSnackbar) {
      final message = customMessage ?? _getUserFriendlyMessage(failure);
      _showUserFeedback(message, failure.severity, onRetry: onRetry);
    }
    
    // Report to crash analytics if critical
    if (failure.severity == FailureSeverity.critical) {
      _reportToCrashAnalytics(failure);
    }
  }
  
  /// Handle an exception and convert it to a failure
  static void handleException(
    Object exception, {
    StackTrace? stackTrace,
    bool showSnackbar = true,
    String? customMessage,
    BuildContext? context,
    VoidCallback? onRetry,
  }) {
    final failure = _exceptionToFailure(exception, stackTrace);
    handleFailure(
      failure,
      showSnackbar: showSnackbar,
      customMessage: customMessage,
      context: context,
      onRetry: onRetry,
    );
  }
  
  /// Handle async operations with automatic error handling
  static Future<T?> handleAsync<T>(
    Future<T> Function() operation, {
    String? errorMessage,
    bool showSnackbar = true,
    VoidCallback? onError,
  }) async {
    try {
      return await operation();
    } catch (error, stackTrace) {
      handleException(
        error,
        stackTrace: stackTrace,
        showSnackbar: showSnackbar,
        customMessage: errorMessage,
      );
      onError?.call();
      return null;
    }
  }
  
  /// Convert exception to failure
  static Failure _exceptionToFailure(Object exception, StackTrace? stackTrace) {
    if (exception is DatabaseException) {
      return Failure.database(
        message: exception.message,
        code: exception.code,
      );
    } else if (exception is NetworkException) {
      return Failure.network(
        message: exception.message,
        code: exception.code,
      );
    } else if (exception is ValidationException) {
      return Failure.invalidInput(
        message: exception.message,
        code: exception.code,
      );
    } else if (exception is NotFoundException) {
      return Failure.notFound(
        message: exception.message,
        code: exception.code,
      );
    } else if (exception is BusinessLogicException) {
      return Failure.businessLogic(
        message: exception.message,
        code: exception.code,
      );
    } else {
      return Failure.unexpected(
        message: exception.toString(),
        stackTrace: stackTrace,
      );
    }
  }
  
  /// Get user-friendly error message
  static String _getUserFriendlyMessage(Failure failure) {
    return failure.when(
      network: (message, code) => 'Network error. Please check your connection and try again.',
      database: (message, code) => 'Data error. Please try again.',
      invalidInput: (message, code) => message ?? 'Invalid input. Please check your data.',
      notFound: (message, code) => 'Item not found.',
      businessLogic: (message, code) => message ?? 'Operation failed.',
      unexpected: (message, stackTrace) => 'An unexpected error occurred. Please try again.',
    );
  }
  
  /// Show user feedback based on error severity
  static void _showUserFeedback(
    String message,
    FailureSeverity severity, {
    VoidCallback? onRetry,
  }) {
    switch (severity) {
      case FailureSeverity.low:
        SnackbarUtils.showInfo(message);
        break;
      case FailureSeverity.medium:
        SnackbarUtils.showWarning(message);
        break;
      case FailureSeverity.high:
        SnackbarUtils.showError(message);
        break;
      case FailureSeverity.critical:
        SnackbarUtils.showError(message);
        if (onRetry != null) {
          SnackbarUtils.showError(
            message,
            action: SnackBarAction(
              label: 'Retry',
              onPressed: onRetry,
            ),
          );
        }
        break;
    }
  }
  
  /// Log error for debugging
  static void _logError(Failure failure) {
    if (kDebugMode) {
      debugPrint('Error: ${failure.message}');
      if (failure.stackTrace != null) {
        debugPrint('Stack trace: ${failure.stackTrace}');
      }
    }
  }
  
  /// Report critical errors to crash analytics
  static void _reportToCrashAnalytics(Failure failure) {
    // In a real app, this would integrate with Firebase Crashlytics,
    // Sentry, or another crash reporting service
    if (kDebugMode) {
      debugPrint('CRITICAL ERROR: ${failure.message}');
    }
  }
}

/// Error boundary widget for catching and handling widget errors
class ErrorBoundary extends StatefulWidget {
  final Widget child;
  final Widget Function(Object error, StackTrace? stackTrace)? errorBuilder;
  final void Function(Object error, StackTrace? stackTrace)? onError;
  
  const ErrorBoundary({
    super.key,
    required this.child,
    this.errorBuilder,
    this.onError,
  });
  
  @override
  State<ErrorBoundary> createState() => _ErrorBoundaryState();
}

class _ErrorBoundaryState extends State<ErrorBoundary> {
  Object? _error;
  StackTrace? _stackTrace;
  
  @override
  Widget build(BuildContext context) {
    if (_error != null) {
      return widget.errorBuilder?.call(_error!, _stackTrace) ??
          _buildDefaultErrorWidget();
    }
    
    return widget.child;
  }
  
  Widget _buildDefaultErrorWidget() {
    return Scaffold(
      body: Center(
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(
                Icons.error_outline,
                size: 64,
                color: Colors.red,
              ),
              const SizedBox(height: 16),
              const Text(
                'Something went wrong',
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              const Text(
                'An unexpected error occurred. Please restart the app.',
                textAlign: TextAlign.center,
                style: TextStyle(color: Colors.grey),
              ),
              const SizedBox(height: 24),
              ElevatedButton(
                onPressed: () {
                  setState(() {
                    _error = null;
                    _stackTrace = null;
                  });
                },
                child: const Text('Try Again'),
              ),
            ],
          ),
        ),
      ),
    );
  }
  
  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    
    // Catch errors in the widget tree
    FlutterError.onError = (FlutterErrorDetails details) {
      setState(() {
        _error = details.exception;
        _stackTrace = details.stack;
      });
      
      widget.onError?.call(details.exception, details.stack);
      UnifiedErrorHandler.handleException(
        details.exception,
        stackTrace: details.stack,
      );
    };
  }
}

/// Extension on Failure for severity classification
extension FailureExtensions on Failure {
  FailureSeverity get severity {
    return when(
      network: (_, __) => FailureSeverity.medium,
      database: (_, __) => FailureSeverity.high,
      invalidInput: (_, __) => FailureSeverity.low,
      notFound: (_, __) => FailureSeverity.low,
      businessLogic: (_, __) => FailureSeverity.medium,
      unexpected: (_, __) => FailureSeverity.critical,
    );
  }
}

/// Error severity levels
enum FailureSeverity {
  low,    // Info/warning level
  medium, // Error level
  high,   // Serious error
  critical, // Critical system error
}

/// Mixin for widgets that need error handling
mixin ErrorHandlingMixin<T extends StatefulWidget> on State<T> {
  /// Handle an error with automatic user feedback
  void handleError(
    Object error, {
    String? customMessage,
    VoidCallback? onRetry,
  }) {
    UnifiedErrorHandler.handleException(
      error,
      showSnackbar: true,
      customMessage: customMessage,
      context: context,
      onRetry: onRetry,
    );
  }
  
  /// Execute an async operation with error handling
  Future<TResult?> executeWithErrorHandling<TResult>(
    Future<TResult> Function() operation, {
    String? errorMessage,
    VoidCallback? onError,
  }) async {
    return UnifiedErrorHandler.handleAsync(
      operation,
      errorMessage: errorMessage,
      onError: onError,
    );
  }
}
