import 'package:dartz/dartz.dart';
import 'package:drift/drift.dart';
import 'package:flutter/foundation.dart';

import '../datasources/app_database.dart';
import '../errors/failures.dart';
import '../services/sync/sync_service.dart';
import 'repository_interfaces.dart';

/// Enhanced base repository that provides comprehensive functionality
/// for all repository implementations with standardized patterns.
/// 
/// This class eliminates code duplication across repository implementations
/// while maintaining type safety and providing consistent error handling,
/// calculation logic placement, and sync integration.
abstract class EnhancedBaseRepository<
  TEntity,
  TData extends DataClass,
  TCompanion extends Insertable<TData>
> implements IRepository<TEntity, int>, ISyncableRepository<TEntity, int> {
  
  final AppDatabase database;
  final SyncService syncService;
  
  EnhancedBaseRepository({
    required this.database,
    required this.syncService,
  });
  
  // Abstract methods that must be implemented by concrete repositories
  
  /// Map domain entity to database companion for inserts/updates
  TCompanion mapToCompanion(TEntity entity);
  
  /// Map database data to domain entity
  TEntity mapFromData(TData data);
  
  /// Get the database insert method for this entity type
  Future<int> insertEntity(TCompanion companion);
  
  /// Get the database update method for this entity type
  Future<bool> updateEntity(TEntity entity);
  
  /// Get the database delete method for this entity type
  Future<bool> deleteEntity(int id);
  
  /// Get all database records for this entity type
  Future<List<TData>> getAllData();
  
  /// Get database record by ID for this entity type
  Future<TData?> getDataById(int id);
  
  /// Get unsynced database records for this entity type
  Future<List<TData>> getUnsyncedData();
  
  /// Mark database record as synced by UUID
  Future<void> markDataAsSynced(String uuid);
  
  // Optional calculation methods (can be overridden)
  
  /// Apply calculations before saving (optional)
  /// Override this method to apply business logic calculations
  Future<Either<Failure, TEntity>> applyCalculations(TEntity entity) async {
    return Right(entity); // Default: no calculations
  }
  
  /// Validate entity before saving (optional)
  /// Override this method to add validation logic
  Future<Either<Failure, TEntity>> validateEntity(TEntity entity) async {
    return Right(entity); // Default: no validation
  }
  
  // Template method implementations with standardized error handling
  
  @override
  Future<Either<Failure, TEntity>> save(TEntity entity) async {
    return executeWithErrorHandling<int, TEntity>(() async {
      // Apply validation
      final validationResult = await validateEntity(entity);
      if (validationResult.isLeft()) {
        return validationResult.fold(
          (failure) => throw Exception(failure.message),
          (_) => throw Exception('Validation failed'),
        );
      }
      
      // Apply calculations
      final calculationResult = await applyCalculations(entity);
      final calculatedEntity = calculationResult.fold(
        (failure) => throw Exception(failure.message),
        (calculatedEntity) => calculatedEntity,
      );
      
      // Save to database
      final companion = mapToCompanion(calculatedEntity);
      final id = await insertEntity(companion);
      _triggerSync();
      return id;
    }, (id) => _copyEntityWithId(entity, id));
  }
  
  @override
  Future<Either<Failure, TEntity>> update(TEntity entity) async {
    return executeWithErrorHandling<bool, TEntity>(() async {
      // Apply validation
      final validationResult = await validateEntity(entity);
      if (validationResult.isLeft()) {
        return validationResult.fold(
          (failure) => throw Exception(failure.message),
          (_) => throw Exception('Validation failed'),
        );
      }
      
      // Apply calculations
      final calculationResult = await applyCalculations(entity);
      final calculatedEntity = calculationResult.fold(
        (failure) => throw Exception(failure.message),
        (calculatedEntity) => calculatedEntity,
      );
      
      // Update in database
      final success = await updateEntity(calculatedEntity);
      if (success) {
        _triggerSync();
      }
      return success;
    }, (success) => success ? entity : throw Exception('Update failed'));
  }
  
  @override
  Future<Either<Failure, bool>> delete(int id) async {
    return executeWithErrorHandling<bool, bool>(() async {
      final success = await deleteEntity(id);
      if (success) {
        _triggerSync();
      }
      return success;
    }, (success) => success);
  }
  
  @override
  Future<Either<Failure, List<TEntity>>> getAll() async {
    return executeWithErrorHandling<List<TData>, List<TEntity>>(
      () => getAllData(),
      (dataList) => dataList.map((data) => mapFromData(data)).toList(),
    );
  }
  
  @override
  Future<Either<Failure, TEntity>> getById(int id) async {
    return executeWithErrorHandling<TData?, TEntity>(() => getDataById(id), (
      data,
    ) {
      if (data == null) {
        throw Exception('Entity with id $id not found');
      }
      return mapFromData(data);
    });
  }
  
  @override
  Future<Either<Failure, List<TEntity>>> getUnsyncedEntities() async {
    return executeWithErrorHandling<List<TData>, List<TEntity>>(
      () => getUnsyncedData(),
      (dataList) => dataList.map((data) => mapFromData(data)).toList(),
    );
  }
  
  @override
  Future<Either<Failure, bool>> markAsSynced(String uuid) async {
    return executeWithErrorHandling<void, bool>(() async {
      await markDataAsSynced(uuid);
    }, (_) => true);
  }
  
  @override
  Future<Either<Failure, bool>> syncEntities() async {
    return executeWithErrorHandling<void, bool>(() async {
      await syncService.syncNow(SyncOperation.full);
    }, (_) => true);
  }
  
  // Helper methods
  
  /// Execute operation with standardized error handling
  Future<Either<Failure, TResult>> executeWithErrorHandling<TOperation, TResult>(
    Future<TOperation> Function() operation,
    TResult Function(TOperation) transform,
  ) async {
    try {
      final result = await operation();
      return Right(transform(result));
    } catch (e) {
      debugPrint('Repository error: $e');
      return Left(_mapExceptionToFailure(e));
    }
  }
  
  /// Map exceptions to failures
  Failure _mapExceptionToFailure(Object exception) {
    if (exception is Exception) {
      final message = exception.toString();
      if (message.contains('database') || message.contains('sql')) {
        return Failure.database(message: message);
      } else if (message.contains('network') || message.contains('connection')) {
        return Failure.network(message: message);
      } else if (message.contains('validation')) {
        return Failure.invalidInput(message: message);
      } else if (message.contains('not found')) {
        return Failure.notFound(message: message);
      }
    }
    return Failure.unexpected(message: exception.toString());
  }
  
  /// Trigger sync operation
  void _triggerSync() {
    syncService.syncData(SyncOperation.upload);
  }
  
  /// Copy entity with new ID (must be implemented by concrete repositories)
  TEntity _copyEntityWithId(TEntity entity, int id);
}

/// Enhanced base repository for entities that support date range filtering
abstract class EnhancedBaseDateRangeRepository<
  TEntity,
  TData extends DataClass,
  TCompanion extends Insertable<TData>
> extends EnhancedBaseRepository<TEntity, TData, TCompanion>
    implements IDateRangeRepository<TEntity, int> {
  
  EnhancedBaseDateRangeRepository({
    required super.database,
    required super.syncService,
  });
  
  // Abstract methods for date range functionality
  
  /// Get data for a specific date range
  Future<List<TData>> getDataForDateRange(DateTime start, DateTime end);
  
  /// Check if data exists for a specific date
  Future<bool> checkDataDateExists(DateTime date, {int? excludeId});
  
  // Implementation of date range interface
  
  @override
  Future<Either<Failure, List<TEntity>>> getForDateRange(
    DateTime start,
    DateTime end,
  ) async {
    return executeWithErrorHandling<List<TData>, List<TEntity>>(
      () => getDataForDateRange(start, end),
      (dataList) => dataList.map((data) => mapFromData(data)).toList(),
    );
  }
  
  @override
  Future<Either<Failure, bool>> checkDateExists(
    DateTime date, {
    int? excludeId,
  }) async {
    return executeWithErrorHandling<bool, bool>(
      () => checkDataDateExists(date, excludeId: excludeId),
      (exists) => exists,
    );
  }
}
