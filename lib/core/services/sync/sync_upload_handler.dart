import 'dart:async';

import 'package:flutter/foundation.dart';

import '../../config/supabase_config.dart';
import '../../datasources/app_database.dart';
import '../../models/sync_models.dart';
import 'generic_entity_sync_handler.dart';
import 'sync_logger.dart';

/// Handler responsible for upload sync operations
class SyncUploadHandler {
  final AppDatabase _db;
  final SupabaseConfig _supabase;
  final SyncLogger _logger;
  final GenericEntitySyncHandler _entitySyncHandler;

  /// Flag to track if database repair is in progress (to prevent infinite loops)
  bool _isRepairInProgress = false;

  /// Constructor
  SyncUploadHandler(this._db, this._supabase, this._logger)
    : _entitySyncHandler = GenericEntitySyncHandler(_supabase, _logger);

  /// Handle upload operation
  Future<bool> handleUploadOperation() async {
    _logger.addLogEntry('Starting upload operation');
    try {
      await uploadPendingChanges();
      return true;
    } catch (e) {
      final errorMessage = 'Upload failed: $e';
      _logger.addLogEntry(errorMessage);
      return false;
    }
  }

  /// Upload pending changes to Supabase
  Future<void> uploadPendingChanges() async {
    try {
      _logger.addLogEntry('Fetching unsynced records from local database');

      // Get all unsynchronized records from each table
      final incomeRecords = await _db.getUnsyncedIncome();
      final orderRecords = await _db.getUnsyncedOrders();
      final performanceRecords = await _db.getUnsyncedPerformance();
      final sparePartRecords = await _db.getUnsyncedSpareParts();
      final sparePartHistoryRecords = await _db.getUnsyncedSparePartsHistory();

      // Log the counts
      _logger.addLogEntry(
        'Found ${incomeRecords.length} unsynced income records',
      );
      _logger.addLogEntry(
        'Found ${orderRecords.length} unsynced order records',
      );
      _logger.addLogEntry(
        'Found ${performanceRecords.length} unsynced performance records',
      );
      _logger.addLogEntry(
        'Found ${sparePartRecords.length} unsynced spare part records',
      );
      _logger.addLogEntry(
        'Found ${sparePartHistoryRecords.length} unsynced spare part history records',
      );

      // Upload each type of record using the generic handler
      await _uploadIncomeRecords(incomeRecords);
      await _uploadOrderRecords(orderRecords);
      await _uploadPerformanceRecords(performanceRecords);
      await _uploadSparePartRecords(sparePartRecords);
      await _uploadSparePartHistoryRecords(sparePartHistoryRecords);

      _logger.addLogEntry('Upload operation completed successfully');
    } catch (e) {
      _logger.addLogEntry('Error uploading pending changes: $e');
      debugPrint('Error uploading pending changes: $e');

      // Check if this is a UUID conflict error and attempt repair (with loop protection)
      if (e.toString().contains('UUID conflict') &&
          e.toString().contains('Database repair may be required')) {
        // Check if we're already in a repair attempt to prevent infinite loops
        if (_isRepairInProgress) {
          _logger.addLogEntry(
            'Database repair already in progress, skipping to prevent infinite loop',
          );
          throw Exception(
            'Sync failed due to UUID conflicts. Database repair already in progress.',
          );
        }

        _logger.addLogEntry(
          'UUID conflict detected, attempting database repair...',
        );
        _isRepairInProgress = true;

        try {
          // Attempt to repair the database
          await _db.repairDatabaseUuids();
          _logger.addLogEntry('Database repair completed, retrying upload...');

          // Retry the upload after repair
          await uploadPendingChanges();
          return;
        } catch (repairError) {
          _logger.addLogEntry('Database repair failed: $repairError');
          throw Exception(
            'Sync failed due to UUID conflicts. Database repair failed: $repairError',
          );
        } finally {
          _isRepairInProgress = false;
        }
      }

      rethrow;
    }
  }

  /// Upload income records
  Future<void> _uploadIncomeRecords(List<IncomeData> records) async {
    await _entitySyncHandler.uploadEntityRecords<IncomeData, IncomeSyncModel>(
      tableName: 'income',
      records: records,
      toSyncModel: (record) => IncomeSyncModel.fromDrift(record),
      markAsSynced: _db.markIncomeAsSynced,
      getUuid: (record) => record.uuid,
      getDeletedAt: (record) => record.deletedAt,
    );
  }

  /// Upload order records
  Future<void> _uploadOrderRecords(List<Order> records) async {
    await _entitySyncHandler.uploadEntityRecords<Order, OrdersSyncModel>(
      tableName: 'orders',
      records: records,
      toSyncModel: (record) => OrdersSyncModel.fromDrift(record),
      markAsSynced: _db.markOrderAsSynced,
      getUuid: (record) => record.uuid,
      getDeletedAt: (record) => record.deletedAt,
    );
  }

  /// Upload performance records
  Future<void> _uploadPerformanceRecords(List<PerformanceData> records) async {
    await _entitySyncHandler
        .uploadEntityRecords<PerformanceData, PerformanceSyncModel>(
          tableName: 'performance',
          records: records,
          toSyncModel: (record) => PerformanceSyncModel.fromDrift(record),
          markAsSynced: _db.markPerformanceAsSynced,
          getUuid: (record) => record.uuid,
          getDeletedAt: (record) => record.deletedAt,
        );
  }

  /// Upload spare part records
  Future<void> _uploadSparePartRecords(List<SparePart> records) async {
    await _entitySyncHandler
        .uploadEntityRecords<SparePart, SparePartsSyncModel>(
          tableName: 'spare_parts',
          records: records,
          toSyncModel: (record) => SparePartsSyncModel.fromDrift(record),
          markAsSynced: _db.markSparePartAsSynced,
          getUuid: (record) => record.uuid,
          getDeletedAt: (record) => record.deletedAt,
        );
  }

  /// Upload spare part history records
  Future<void> _uploadSparePartHistoryRecords(
    List<SparePartsHistoryData> records,
  ) async {
    await _entitySyncHandler
        .uploadEntityRecords<SparePartsHistoryData, SparePartsHistorySyncModel>(
          tableName: 'spare_parts_history',
          records: records,
          toSyncModel: (record) => SparePartsHistorySyncModel.fromDrift(record),
          markAsSynced: _db.markSparePartHistoryAsSynced,
          getUuid: (record) => record.uuid,
          getDeletedAt: (record) => record.deletedAt,
        );
  }
}
