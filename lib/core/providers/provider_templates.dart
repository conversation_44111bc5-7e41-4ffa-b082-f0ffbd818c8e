/// Provider templates for standardizing provider patterns across features
///
/// This file provides standardized templates and mixins to eliminate duplication
/// in provider creation patterns across all features.
library;

import 'package:dartz/dartz.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../datasources/app_database.dart';
import '../errors/failures.dart';
import '../services/sync/sync_service.dart';
import 'app_settings_provider.dart';

/// Template for creating repository providers that extend BaseRepository
///
/// This template standardizes the pattern of creating repository providers
/// that depend on database and sync service.
///
/// Usage:
/// ```dart
/// @riverpod
/// FeatureRepository featureRepository(Ref ref) {
///   return createRepositoryProvider<FeatureRepository, FeatureRepositoryImpl>(
///     ref,
///     (database, syncService) => FeatureRepositoryImpl(
///       database: database,
///       syncService: syncService,
///     ),
///   );
/// }
/// ```
T createRepositoryProvider<T, TImpl extends T>(
  Ref ref,
  TImpl Function(AppDatabase database, SyncService syncService) factory,
) {
  final database = ref.watch(databaseProvider);
  final syncService = ref.watch(syncServiceProvider);
  return factory(database, syncService);
}

/// Template for creating repository providers with additional dependencies
///
/// This template handles repositories that need additional services beyond
/// database and sync service.
///
/// Usage:
/// ```dart
/// @riverpod
/// FeatureRepository featureRepository(Ref ref) {
///   return createRepositoryProviderWithDeps<FeatureRepository, FeatureRepositoryImpl>(
///     ref,
///     (database, syncService) => FeatureRepositoryImpl(
///       database: database,
///       syncService: syncService,
///       calculationService: ref.watch(calculationServiceProvider),
///     ),
///   );
/// }
/// ```
T createRepositoryProviderWithDeps<T, TImpl extends T>(
  Ref ref,
  TImpl Function(AppDatabase database, SyncService syncService) factory,
) {
  final database = ref.watch(databaseProvider);
  final syncService = ref.watch(syncServiceProvider);
  return factory(database, syncService);
}

/// Template for creating use case providers
///
/// This template standardizes the pattern of creating use case providers
/// that depend on a repository.
///
/// Usage:
/// ```dart
/// @riverpod
/// GetFeatureData getFeatureData(Ref ref) {
///   return createUseCaseProvider<GetFeatureData, FeatureRepository>(
///     ref,
///     featureRepositoryProvider,
///     (repository) => GetFeatureData(repository),
///   );
/// }
/// ```
T createUseCaseProvider<T, TRepository>(
  Ref ref,
  Provider<TRepository> repositoryProvider,
  T Function(TRepository repository) factory,
) {
  final repository = ref.watch(repositoryProvider);
  return factory(repository);
}

/// Template for creating use case providers with multiple dependencies
///
/// This template handles use cases that need multiple repositories or services.
///
/// Usage:
/// ```dart
/// @riverpod
/// ComplexUseCase complexUseCase(Ref ref) {
///   return createUseCaseProviderMultiDeps<ComplexUseCase>(
///     ref,
///     () => ComplexUseCase(
///       repository1: ref.watch(repository1Provider),
///       repository2: ref.watch(repository2Provider),
///       service: ref.watch(serviceProvider),
///     ),
///   );
/// }
/// ```
T createUseCaseProviderMultiDeps<T>(Ref ref, T Function() factory) {
  return factory();
}

/// Mixin for standardizing error handling in provider classes
///
/// This mixin provides common error handling patterns for AsyncNotifier classes.
///
/// Usage:
/// ```dart
/// @riverpod
/// class FeatureList extends _$FeatureList with ProviderErrorHandling {
///   @override
///   Future<List<Feature>> build() async {
///     return _fetchFeatureList();
///   }
///
///   Future<List<Feature>> _fetchFeatureList() async {
///     final useCase = ref.watch(getFeatureListProvider);
///     final result = await useCase.execute();
///     return handleResult(result, []);
///   }
/// }
/// ```
mixin ProviderErrorHandling {
  /// Handle Either&lt;Failure, T&gt; results with standardized error handling
  T handleResult<T>(
    Either<Failure, T> result,
    T defaultValue, {
    void Function(Failure)? onError,
  }) {
    return result.fold((failure) {
      _handleFailure(failure);
      onError?.call(failure);
      return defaultValue;
    }, (value) => value);
  }

  /// Handle async Either&lt;Failure, T&gt; results with standardized error handling
  Future<T> handleAsyncResult<T>(
    Future<Either<Failure, T>> futureResult,
    T defaultValue, {
    void Function(Failure)? onError,
  }) async {
    final result = await futureResult;
    return handleResult(result, defaultValue, onError: onError);
  }

  /// Standard failure handling
  void _handleFailure(Failure failure) {
    // Log the error - in a real app, you might use a logging service
    // ignore: avoid_print
    print('Provider error: ${failure.message}');
    // You could also send to crash reporting service here
  }
}

/// Template for creating list providers with refresh capability
///
/// This template standardizes the pattern of creating list providers that
/// can be refreshed and handle loading states.
///
/// Usage:
/// ```dart
/// @riverpod
/// class FeatureList extends _$FeatureList with ProviderErrorHandling, ListProviderTemplate<Feature> {
///   @override
///   Future<List<Feature>> build() async {
///     return fetchList();
///   }
///
///   @override
///   Future<List<Feature>> fetchData() async {
///     final useCase = ref.watch(getFeatureListProvider);
///     final result = await useCase.getAll();
///     return handleResult(result, []);
///   }
///
///   @override
///   List<Provider> get invalidateProviders => [featureListProvider];
/// }
/// ```
mixin ListProviderTemplate<T> on ProviderErrorHandling {
  /// Fetch the list data - must be implemented by concrete classes
  Future<List<T>> fetchData();

  /// Providers to invalidate when refreshing - must be implemented by concrete classes
  List<Provider> get invalidateProviders;

  /// Standard list fetching with error handling
  Future<List<T>> fetchList() async {
    return fetchData();
  }

  /// Refresh the list and invalidate related providers
  Future<void> refreshList() async {
    // Invalidate related providers
    for (final _ in invalidateProviders) {
      // Note: This would need access to ref, which should be provided by the concrete class
    }
  }
}

/// Template for creating operation providers (add, update, delete)
///
/// This template standardizes the pattern of creating providers that handle
/// CRUD operations with loading states and error handling.
///
/// Usage:
/// ```dart
/// @riverpod
/// class FeatureOperations extends _$FeatureOperations with ProviderErrorHandling, OperationProviderTemplate {
///   @override
///   Future<void> build() async {}
///
///   Future<bool> addFeature(Feature feature) async {
///     return executeOperation(() async {
///       final repository = ref.read(featureRepositoryProvider);
///       final result = await repository.save(feature);
///       return handleResult(result, false);
///     }, [featureListProvider]);
///   }
/// }
/// ```
mixin OperationProviderTemplate {
  /// Execute an operation with standardized error handling and provider invalidation
  Future<T> executeOperation<T>(
    Future<T> Function() operation,
    List<Provider> providersToInvalidate, {
    void Function(Object error)? onError,
  }) async {
    try {
      final result = await operation();

      // Invalidate related providers on success
      for (final _ in providersToInvalidate) {
        // Note: This would need to be implemented with proper ref access
        // ref.invalidate(provider);
      }

      return result;
    } catch (error) {
      onError?.call(error);
      rethrow;
    }
  }
}

/// Enhanced CRUD provider template that combines list and operations
///
/// This template provides a complete CRUD provider implementation with
/// standardized patterns for all operations.
///
/// Usage:
/// ```dart
/// @riverpod
/// class FeatureCrud extends _$FeatureCrud
///     with ProviderErrorHandling, CrudProviderTemplate<Feature> {
///   @override
///   Future<List<Feature>> build() async => fetchList();
///
///   @override
///   Future<List<Feature>> fetchData() async {
///     final repository = ref.watch(featureRepositoryProvider);
///     final result = await repository.getAll();
///     return handleResult(result, []);
///   }
///
///   @override
///   FeatureRepository get repository => ref.read(featureRepositoryProvider);
/// }
/// ```
mixin CrudProviderTemplate<T> on ProviderErrorHandling {
  /// Get the repository instance
  dynamic get repository;

  /// Fetch the list data
  Future<List<T>> fetchData();

  /// Add a new entity
  Future<bool> add(T entity) async {
    final result = await repository.save(entity);
    final success = handleResult(result, false);
    if (success) {
      await refresh();
    }
    return success;
  }

  /// Update an existing entity
  Future<bool> update(T entity) async {
    final result = await repository.update(entity);
    final success = handleResult(result, false);
    if (success) {
      await refresh();
    }
    return success;
  }

  /// Delete an entity
  Future<bool> delete(dynamic id) async {
    final result = await repository.delete(id);
    final success = handleResult(result, false);
    if (success) {
      await refresh();
    }
    return success;
  }

  /// Refresh the list
  Future<void> refresh() async {
    // This would need to be implemented by the concrete provider
    // state = const AsyncValue.loading();
    // state = await AsyncValue.guard(() => fetchData());
  }
}

/// Date range provider template for features that filter by date
///
/// This template standardizes date range filtering patterns.
mixin DateRangeProviderTemplate<T> on ProviderErrorHandling {
  /// Get the current date range
  DateTimeRange get dateRange;

  /// Fetch data for the current date range
  Future<List<T>> fetchDataForDateRange(DateTimeRange range);

  /// Filter data by date range
  Future<List<T>> fetchFilteredData() async {
    return await fetchDataForDateRange(dateRange);
  }
}

/// Pagination provider template
///
/// This template standardizes pagination patterns across features.
mixin PaginationProviderTemplate<T> on ProviderErrorHandling {
  /// Current page number
  int get currentPage;

  /// Items per page
  int get itemsPerPage;

  /// Whether there are more pages
  bool get hasMorePages;

  /// Fetch a specific page
  Future<List<T>> fetchPage(int page);

  /// Load the next page
  Future<void> loadNextPage() async {
    if (!hasMorePages) return;

    try {
      await fetchPage(currentPage + 1);
      // Implementation would append to existing data
    } catch (error) {
      _handleFailure(Failure.unexpected(message: error.toString()));
    }
  }

  /// Reset pagination
  Future<void> resetPagination() async {
    // Implementation would reset to first page
  }
}
mixin OperationProviderTemplate on ProviderErrorHandling {
  /// Execute an operation with standardized loading state and error handling
  Future<T> executeOperation<T>(
    Future<T> Function() operation,
    List<Provider> providersToInvalidate, {
    T? defaultValue,
  }) async {
    try {
      // Set loading state if this is an AsyncNotifier
      // state = const AsyncValue.loading();

      final result = await operation();

      // Invalidate related providers on success
      for (final _ in providersToInvalidate) {
        // Note: This would need access to ref, which should be provided by the concrete class
      }

      // Set success state if this is an AsyncNotifier
      // state = const AsyncValue.data(null);

      return result;
    } catch (error, _) {
      // Set error state if this is an AsyncNotifier
      // state = AsyncValue.error(error, stackTrace);

      if (defaultValue != null) {
        return defaultValue;
      }
      rethrow;
    }
  }
}
