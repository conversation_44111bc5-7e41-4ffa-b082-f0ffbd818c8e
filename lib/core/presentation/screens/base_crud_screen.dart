import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

import '../../theme/app_colors.dart';
import '../../utils/date_helper.dart';
import '../../utils/snackbar_utils.dart';
import '../../widgets/date_range_selector_field.dart';
import '../../widgets/item_actions_bottom_sheet.dart';
import '../widgets/app_delete_confirmation_dialog.dart';
import '../widgets/app_error_container.dart';

/// Abstract base class for CRUD screens with common functionality
/// 
/// This class provides a standardized structure for screens that display
/// lists of entities with CRUD operations, date range filtering, and
/// consistent UI patterns.
/// 
/// Type Parameters:
/// - [TEntity]: The domain entity type (e.g., Order, Income, Performance)
/// - [TSummary]: The summary data type for the screen
abstract class BaseCrudScreen<TEntity, TSummary> extends ConsumerStatefulWidget {
  const BaseCrudScreen({super.key});
}

/// Abstract state class for CRUD screens
abstract class BaseCrudScreenState<TEntity, TSummary, T extends BaseCrudScreen<TEntity, TSummary>>
    extends ConsumerState<T> {
  
  // Abstract properties that must be implemented by concrete screens
  
  /// The title displayed in the app bar
  String get screenTitle;
  
  /// The tooltip text for the add button
  String get addButtonTooltip;
  
  /// The icon used for the entity in action sheets
  IconData get entityIcon;
  
  /// The name of the entity for display purposes
  String get entityName;
  
  // Abstract methods for data access
  
  /// Provider for the entity list
  AsyncValue<List<TEntity>> get entityListAsync;
  
  /// Provider for the date range
  AsyncValue<DateTimeRange> get dateRangeAsync;
  
  /// Provider for the summary data (optional)
  AsyncValue<TSummary>? get summaryAsync => null;
  
  /// Check if any async values are in loading state
  bool get isLoading;
  
  // Abstract methods for UI building
  
  /// Build the summary section (optional)
  Widget? buildSummarySection(BuildContext context, TSummary? summary) => null;
  
  /// Build a list item widget for the entity
  Widget buildListItem(TEntity entity);
  
  /// Build the empty state widget
  Widget buildEmptyState(BuildContext context);
  
  /// Build loading state widget (optional override)
  Widget buildLoadingState() => const Center(child: CircularProgressIndicator());
  
  // Abstract methods for actions
  
  /// Navigate to the form screen for adding/editing
  void navigateToForm(BuildContext context, {TEntity? entity});
  
  /// Show entity details (optional)
  void showEntityDetails(BuildContext context, TEntity entity) {}
  
  /// Delete an entity
  Future<bool> deleteEntity(TEntity entity);
  
  /// Refresh the entity list
  Future<void> refreshEntityList();
  
  /// Get display text for an entity (used in delete confirmation)
  String getEntityDisplayText(TEntity entity);
  
  // Common UI building methods
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: RefreshIndicator(
        onRefresh: refreshEntityList,
        child: CustomScrollView(
          slivers: [
            _buildAppBar(),
            
            if (isLoading)
              SliverToBoxAdapter(child: buildLoadingState())
            else ...[
              if (summaryAsync != null) _buildSummarySection(),
              _buildHistoryHeader(),
              _buildEntityListSection(),
            ],
          ],
        ),
      ),
    );
  }
  
  /// Build the app bar with date range selector
  Widget _buildAppBar() {
    return SliverAppBar(
      floating: true,
      pinned: true,
      elevation: 0,
      backgroundColor: AppColors.primary,
      title: Text(screenTitle),
      bottom: PreferredSize(
        preferredSize: const Size.fromHeight(60),
        child: Padding(
          padding: const EdgeInsets.fromLTRB(16.0, 4.0, 16.0, 12.0),
          child: dateRangeAsync.when(
            data: (dateRange) => _buildDateRangeSelector(dateRange),
            loading: () => _buildDateRangeSelectorShimmer(),
            error: (error, stack) => AppErrorContainer(error: error),
          ),
        ),
      ),
      actions: [
        IconButton(
          icon: const Icon(Icons.add),
          tooltip: addButtonTooltip,
          onPressed: () => navigateToForm(context),
        ),
      ],
    );
  }
  
  /// Build the date range selector widget
  Widget _buildDateRangeSelector(DateTimeRange dateRange) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white.withAlpha(30),
        borderRadius: BorderRadius.circular(8),
      ),
      child: DateRangeSelectorField(
        dateRange: dateRange,
        onDateRangeSelected: onDateRangeSelected,
      ),
    );
  }
  
  /// Handle date range selection
  void onDateRangeSelected(DateTimeRange newRange) {
    // This should be implemented by concrete screens to update their date range provider
  }
  
  /// Build shimmer for date range selector
  Widget _buildDateRangeSelectorShimmer() {
    return Container(
      height: 40,
      decoration: BoxDecoration(
        color: Colors.white.withAlpha(30),
        borderRadius: BorderRadius.circular(8),
      ),
      child: const Center(
        child: SizedBox(
          width: 20,
          height: 20,
          child: CircularProgressIndicator(
            strokeWidth: 2,
            valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
          ),
        ),
      ),
    );
  }
  
  /// Build the summary section if summary data is available
  Widget _buildSummarySection() {
    final summary = summaryAsync;
    if (summary == null) return const SliverToBoxAdapter(child: SizedBox.shrink());
    
    return SliverToBoxAdapter(
      child: summary.when(
        data: (summaryData) {
          final summaryWidget = buildSummarySection(context, summaryData);
          return summaryWidget ?? const SizedBox.shrink();
        },
        loading: () => const Padding(
          padding: EdgeInsets.all(16.0),
          child: Center(child: CircularProgressIndicator()),
        ),
        error: (error, stack) => Padding(
          padding: const EdgeInsets.all(16.0),
          child: AppErrorContainer(error: error),
        ),
      ),
    );
  }
  
  /// Build the history header section
  Widget _buildHistoryHeader() {
    return SliverToBoxAdapter(
      child: Padding(
        padding: const EdgeInsets.fromLTRB(16.0, 24.0, 16.0, 8.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'History',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            Text(
              'Your recent ${entityName.toLowerCase()} records',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Colors.grey[600],
              ),
            ),
          ],
        ),
      ),
    );
  }
  
  /// Build the entity list section
  Widget _buildEntityListSection() {
    return entityListAsync.when(
      data: (entityList) => _buildEntityList(entityList),
      loading: () => SliverToBoxAdapter(child: buildLoadingState()),
      error: (error, stack) => SliverFillRemaining(
        child: Center(
          child: AppErrorContainer(
            error: error,
            onRetry: refreshEntityList,
          ),
        ),
      ),
    );
  }
  
  /// Build the entity list or empty state
  Widget _buildEntityList(List<TEntity> entityList) {
    if (entityList.isEmpty) {
      return SliverFillRemaining(
        child: buildEmptyState(context),
      );
    }
    
    return SliverList(
      delegate: SliverChildBuilderDelegate(
        (context, index) {
          final entity = entityList[index];
          return buildListItem(entity);
        },
        childCount: entityList.length,
      ),
    );
  }
  
  /// Show actions bottom sheet for an entity
  void showActionsBottomSheet(BuildContext context, TEntity entity) {
    ItemActionsBottomSheet.show(
      context: context,
      title: entityName,
      subtitle: getEntityDisplayText(entity),
      onEdit: () => navigateToForm(context, entity: entity),
      onDelete: () => _showDeleteConfirmation(context, entity),
      itemIcon: entityIcon,
    );
  }
  
  /// Show delete confirmation dialog
  void _showDeleteConfirmation(BuildContext context, TEntity entity) {
    AppDeleteConfirmationDialog.show(
      context: context,
      title: 'Delete $entityName',
      message: 'Are you sure you want to delete ${getEntityDisplayText(entity)}?',
      onConfirm: () => _handleDelete(entity),
    );
  }
  
  /// Handle entity deletion
  void _handleDelete(TEntity entity) async {
    SnackbarUtils.showLoading(message: 'Deleting...');
    
    final success = await deleteEntity(entity);
    
    if (mounted) {
      if (success) {
        SnackbarUtils.showSuccess('$entityName deleted successfully');
        await refreshEntityList();
      } else {
        SnackbarUtils.showError('Failed to delete $entityName');
      }
    }
  }
}
