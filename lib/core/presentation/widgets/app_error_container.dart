import 'package:flutter/material.dart';

/// A standardized error container widget for displaying errors consistently
/// across the application.
/// 
/// This widget provides a consistent way to display errors with optional
/// retry functionality and customizable styling.
class AppErrorContainer extends StatelessWidget {
  /// The error object to display
  final Object error;
  
  /// Optional callback for retry functionality
  final VoidCallback? onRetry;
  
  /// Whether to show the retry button
  final bool showRetry;
  
  /// Custom error message override
  final String? customMessage;
  
  /// Whether to use compact styling
  final bool compact;
  
  /// Background color override
  final Color? backgroundColor;
  
  /// Text color override
  final Color? textColor;
  
  /// Icon color override
  final Color? iconColor;
  
  const AppErrorContainer({
    super.key,
    required this.error,
    this.onRetry,
    this.showRetry = true,
    this.customMessage,
    this.compact = false,
    this.backgroundColor,
    this.textColor,
    this.iconColor,
  });
  
  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final errorColor = theme.colorScheme.error;
    
    final displayMessage = customMessage ?? _getErrorMessage(error);
    final bgColor = backgroundColor ?? errorColor.withAlpha(30);
    final txtColor = textColor ?? errorColor;
    final icnColor = iconColor ?? errorColor;
    
    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: compact ? 8 : 12,
        vertical: compact ? 6 : 8,
      ),
      decoration: BoxDecoration(
        color: bgColor,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: errorColor.withAlpha(50),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Icon(
            Icons.error_outline,
            color: icnColor,
            size: compact ? 16 : 20,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              displayMessage,
              style: theme.textTheme.bodyMedium?.copyWith(
                color: txtColor,
                fontSize: compact ? 12 : null,
              ),
            ),
          ),
          if (showRetry && onRetry != null) ...[
            const SizedBox(width: 8),
            InkWell(
              onTap: onRetry,
              borderRadius: BorderRadius.circular(4),
              child: Padding(
                padding: const EdgeInsets.all(4),
                child: Icon(
                  Icons.refresh,
                  color: icnColor,
                  size: compact ? 16 : 20,
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }
  
  /// Extract a user-friendly error message from the error object
  String _getErrorMessage(Object error) {
    if (error is Exception) {
      final message = error.toString();
      // Remove "Exception: " prefix if present
      if (message.startsWith('Exception: ')) {
        return message.substring(11);
      }
      return message;
    }
    
    // For other error types, provide a generic message
    return 'An error occurred: ${error.toString()}';
  }
}

/// A specialized error container for network errors
class NetworkErrorContainer extends AppErrorContainer {
  const NetworkErrorContainer({
    super.key,
    required super.error,
    super.onRetry,
  }) : super(
    customMessage: 'Network error. Please check your connection and try again.',
    backgroundColor: Colors.orange.withAlpha(30),
    textColor: Colors.orange.shade700,
    iconColor: Colors.orange.shade700,
  );
}

/// A specialized error container for validation errors
class ValidationErrorContainer extends AppErrorContainer {
  const ValidationErrorContainer({
    super.key,
    required super.error,
    super.onRetry,
  }) : super(
    showRetry: false,
    backgroundColor: Colors.amber.withAlpha(30),
    textColor: Colors.amber.shade700,
    iconColor: Colors.amber.shade700,
  );
}

/// A specialized error container for loading errors
class LoadingErrorContainer extends AppErrorContainer {
  const LoadingErrorContainer({
    super.key,
    required super.error,
    super.onRetry,
  }) : super(
    customMessage: 'Failed to load data. Tap to retry.',
    showRetry: true,
  );
}
