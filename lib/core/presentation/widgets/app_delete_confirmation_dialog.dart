import 'package:flutter/material.dart';

/// A standardized delete confirmation dialog for consistent user experience
/// across the application.
/// 
/// This dialog provides a consistent way to confirm delete operations with
/// customizable title, message, and button text.
class AppDeleteConfirmationDialog {
  /// Show a delete confirmation dialog
  /// 
  /// Returns `true` if the user confirms the deletion, `false` if they cancel,
  /// or `null` if the dialog is dismissed without a selection.
  static Future<bool?> show({
    required BuildContext context,
    required String title,
    required String message,
    String confirmText = 'Delete',
    String cancelText = 'Cancel',
    VoidCallback? onConfirm,
    VoidCallback? onCancel,
    Color? confirmButtonColor,
    IconData? icon,
  }) {
    return showDialog<bool>(
      context: context,
      barrierDismissible: false, // Prevent accidental dismissal
      builder: (context) => _DeleteConfirmationDialog(
        title: title,
        message: message,
        confirmText: confirmText,
        cancelText: cancelText,
        onConfirm: onConfirm,
        onCancel: onCancel,
        confirmButtonColor: confirmButtonColor,
        icon: icon,
      ),
    );
  }
  
  /// Show a delete confirmation dialog with a callback-based approach
  /// 
  /// This method automatically handles the dialog result and calls the
  /// appropriate callback based on the user's choice.
  static Future<void> showWithCallback({
    required BuildContext context,
    required String title,
    required String message,
    required VoidCallback onConfirm,
    VoidCallback? onCancel,
    String confirmText = 'Delete',
    String cancelText = 'Cancel',
    Color? confirmButtonColor,
    IconData? icon,
  }) async {
    final result = await show(
      context: context,
      title: title,
      message: message,
      confirmText: confirmText,
      cancelText: cancelText,
      confirmButtonColor: confirmButtonColor,
      icon: icon,
    );
    
    if (result == true) {
      onConfirm();
    } else if (result == false && onCancel != null) {
      onCancel();
    }
  }
}

/// Internal dialog widget implementation
class _DeleteConfirmationDialog extends StatelessWidget {
  final String title;
  final String message;
  final String confirmText;
  final String cancelText;
  final VoidCallback? onConfirm;
  final VoidCallback? onCancel;
  final Color? confirmButtonColor;
  final IconData? icon;
  
  const _DeleteConfirmationDialog({
    required this.title,
    required this.message,
    required this.confirmText,
    required this.cancelText,
    this.onConfirm,
    this.onCancel,
    this.confirmButtonColor,
    this.icon,
  });
  
  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    
    return AlertDialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      title: Row(
        children: [
          if (icon != null) ...[
            Icon(
              icon,
              color: confirmButtonColor ?? colorScheme.error,
              size: 24,
            ),
            const SizedBox(width: 12),
          ],
          Expanded(
            child: Text(
              title,
              style: theme.textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
      content: Text(
        message,
        style: theme.textTheme.bodyMedium,
      ),
      actions: [
        TextButton(
          onPressed: () {
            Navigator.of(context).pop(false);
            onCancel?.call();
          },
          child: Text(
            cancelText,
            style: TextStyle(
              color: colorScheme.onSurface.withAlpha(180),
            ),
          ),
        ),
        TextButton(
          onPressed: () {
            Navigator.of(context).pop(true);
            onConfirm?.call();
          },
          style: TextButton.styleFrom(
            foregroundColor: confirmButtonColor ?? colorScheme.error,
          ),
          child: Text(
            confirmText,
            style: const TextStyle(
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
      ],
    );
  }
}

/// Specialized delete confirmation dialogs for common use cases

/// Delete confirmation for records with dates
class RecordDeleteConfirmationDialog {
  static Future<bool?> show({
    required BuildContext context,
    required String recordType,
    required DateTime date,
    VoidCallback? onConfirm,
  }) {
    return AppDeleteConfirmationDialog.show(
      context: context,
      title: 'Delete $recordType Record',
      message: 'Are you sure you want to delete the $recordType record for ${_formatDate(date)}?',
      onConfirm: onConfirm,
      icon: Icons.delete_outline,
    );
  }
  
  static String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }
}

/// Delete confirmation for items with names
class ItemDeleteConfirmationDialog {
  static Future<bool?> show({
    required BuildContext context,
    required String itemType,
    required String itemName,
    VoidCallback? onConfirm,
  }) {
    return AppDeleteConfirmationDialog.show(
      context: context,
      title: 'Delete $itemType',
      message: 'Are you sure you want to delete "$itemName"?',
      onConfirm: onConfirm,
      icon: Icons.delete_outline,
    );
  }
}

/// Bulk delete confirmation
class BulkDeleteConfirmationDialog {
  static Future<bool?> show({
    required BuildContext context,
    required String itemType,
    required int count,
    VoidCallback? onConfirm,
  }) {
    return AppDeleteConfirmationDialog.show(
      context: context,
      title: 'Delete Multiple Items',
      message: 'Are you sure you want to delete $count $itemType${count > 1 ? 's' : ''}?',
      confirmText: 'Delete All',
      onConfirm: onConfirm,
      icon: Icons.delete_sweep,
      confirmButtonColor: Colors.red.shade700,
    );
  }
}
