import 'dart:async';
import 'dart:convert';
import 'package:dartz/dartz.dart';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../../../core/datasources/app_database.dart';
import '../../../../core/errors/failures.dart';
import '../../../../core/repositories/app_settings_repository.dart';
import '../../domain/entities/app_settings.dart';
import '../../domain/entities/backup_settings.dart';
import '../../domain/entities/date_range_settings.dart';
import '../../domain/repositories/settings_repository.dart';

/// Implementation of SettingsRepository using the existing core app settings repository
class SettingsRepositoryImpl implements SettingsRepository {
  final AppSettingsRepository _coreRepository;
  static const String _backupSettingsKey = 'backup_settings';
  static const String _dateRangeSettingsKey = 'date_range_settings';

  StreamController<Either<Failure, AppSettings>>? _settingsController;
  AppSettings? _cachedSettings;

  SettingsRepositoryImpl(this._coreRepository);

  @override
  bool get isInitialized => _cachedSettings != null;

  @override
  Future<Either<Failure, bool>> initialize() async {
    try {
      // Load initial settings
      final settingsResult = await getOrCreateAppSettings();
      settingsResult.fold(
        (failure) => null,
        (settings) => _cachedSettings = settings,
      );

      _setupSettingsStream();
      return const Right(true);
    } catch (e) {
      return Left(Failure.unexpected(message: 'Failed to initialize settings repository: $e'));
    }
  }

  @override
  Future<Either<Failure, AppSettings>> getAppSettings() async {
    try {
      if (_cachedSettings != null) {
        return Right(_cachedSettings!);
      }

      final dbSettings = await _coreRepository.getAppSettings();
      if (dbSettings != null) {
        final settings = AppSettings.fromDatabase(dbSettings);
        _cachedSettings = settings;
        return Right(settings);
      }

      // Return default settings if none exist
      final defaultSettings = AppSettings.defaultSettings();
      _cachedSettings = defaultSettings;
      return Right(defaultSettings);
    } catch (e) {
      return Left(Failure.unexpected(message: 'Failed to get app settings: $e'));
    }
  }

  @override
  Future<Either<Failure, AppSettings>> updateAppSettings(AppSettings settings) async {
    try {
      // Convert to database format and update
      final companion = AppSettingsCompanion.insert(
        dateRangeStart: settings.dateRangeStart,
        dateRangeEnd: settings.dateRangeEnd,
        backupDirectoryPath: Value(settings.backupDirectoryPath),
        updatedAt: Value(settings.updatedAt),
        lastSyncTime: Value(settings.lastSyncTime),
      );

      if (settings.id != null) {
        await _coreRepository.updateAppSettings(companion.copyWith(id: Value(settings.id!)));
      } else {
        await _coreRepository.insertAppSettings(companion);
      }

      _cachedSettings = settings;
      _settingsController?.add(Right(settings));

      return Right(settings);
    } catch (e) {
      return Left(Failure.unexpected(message: 'Failed to update app settings: $e'));
    }
  }

  @override
  Future<Either<Failure, AppSettings>> getOrCreateAppSettings() async {
    try {
      final dbSettings = await _coreRepository.getOrCreateAppSettings();
      final settings = AppSettings.fromDatabase(dbSettings);
      _cachedSettings = settings;
      return Right(settings);
    } catch (e) {
      return Left(Failure.unexpected(message: 'Failed to get or create app settings: $e'));
    }
  }

  @override
  Future<Either<Failure, AppSettings>> updateDateRange(DateTimeRange dateRange) async {
    final currentSettingsResult = await getAppSettings();
    
    return currentSettingsResult.fold(
      (failure) => Left(failure),
      (currentSettings) async {
        final updatedSettings = currentSettings.copyWith(
          dateRangeStart: dateRange.start,
          dateRangeEnd: dateRange.end,
          updatedAt: DateTime.now().toUtc(),
        );
        
        return await updateAppSettings(updatedSettings);
      },
    );
  }

  @override
  Future<Either<Failure, DateRangeSettings>> getDateRangeSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final settingsJson = prefs.getString(_dateRangeSettingsKey);
      
      if (settingsJson != null) {
        final json = jsonDecode(settingsJson) as Map<String, dynamic>;
        return Right(DateRangeSettings.fromJson(json));
      }

      // Return current month as default
      final defaultSettings = DateRangeSettings.currentMonth();
      return Right(defaultSettings);
    } catch (e) {
      return Left(Failure.unexpected(message: 'Failed to get date range settings: $e'));
    }
  }

  @override
  Future<Either<Failure, DateRangeSettings>> updateDateRangeSettings(
    DateRangeSettings settings,
  ) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final settingsJson = jsonEncode(settings.toJson());
      await prefs.setString(_dateRangeSettingsKey, settingsJson);
      
      return Right(settings);
    } catch (e) {
      return Left(Failure.unexpected(message: 'Failed to update date range settings: $e'));
    }
  }

  @override
  Future<Either<Failure, BackupSettings>> getBackupSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final settingsJson = prefs.getString(_backupSettingsKey);
      
      if (settingsJson != null) {
        final json = jsonDecode(settingsJson) as Map<String, dynamic>;
        return Right(BackupSettings.fromJson(json));
      }

      // Return default backup settings
      final defaultSettings = BackupSettings.defaultSettings();
      return Right(defaultSettings);
    } catch (e) {
      return Left(Failure.unexpected(message: 'Failed to get backup settings: $e'));
    }
  }

  @override
  Future<Either<Failure, BackupSettings>> updateBackupSettings(
    BackupSettings settings,
  ) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final settingsJson = jsonEncode(settings.toJson());
      await prefs.setString(_backupSettingsKey, settingsJson);
      
      return Right(settings);
    } catch (e) {
      return Left(Failure.unexpected(message: 'Failed to update backup settings: $e'));
    }
  }

  @override
  Future<Either<Failure, AppSettings>> updateBackupDirectoryPath(String? path) async {
    final currentSettingsResult = await getAppSettings();
    
    return currentSettingsResult.fold(
      (failure) => Left(failure),
      (currentSettings) async {
        final updatedSettings = currentSettings.copyWith(
          backupDirectoryPath: path,
          updatedAt: DateTime.now().toUtc(),
        );
        
        return await updateAppSettings(updatedSettings);
      },
    );
  }

  @override
  Future<Either<Failure, AppSettings>> updateLastSyncTime(DateTime syncTime) async {
    final currentSettingsResult = await getAppSettings();
    
    return currentSettingsResult.fold(
      (failure) => Left(failure),
      (currentSettings) async {
        final updatedSettings = currentSettings.copyWith(
          lastSyncTime: syncTime,
          updatedAt: DateTime.now().toUtc(),
        );
        
        return await updateAppSettings(updatedSettings);
      },
    );
  }

  @override
  Future<Either<Failure, AppSettings>> setSyncEnabled(bool enabled) async {
    final currentSettingsResult = await getAppSettings();
    
    return currentSettingsResult.fold(
      (failure) => Left(failure),
      (currentSettings) async {
        final updatedSettings = currentSettings.copyWith(
          syncEnabled: enabled,
          updatedAt: DateTime.now().toUtc(),
        );
        
        return await updateAppSettings(updatedSettings);
      },
    );
  }

  @override
  Future<Either<Failure, AppSettings>> setNotificationsEnabled(bool enabled) async {
    final currentSettingsResult = await getAppSettings();
    
    return currentSettingsResult.fold(
      (failure) => Left(failure),
      (currentSettings) async {
        final updatedSettings = currentSettings.copyWith(
          notificationsEnabled: enabled,
          updatedAt: DateTime.now().toUtc(),
        );
        
        return await updateAppSettings(updatedSettings);
      },
    );
  }

  @override
  Future<Either<Failure, AppSettings>> setDarkModeEnabled(bool enabled) async {
    final currentSettingsResult = await getAppSettings();
    
    return currentSettingsResult.fold(
      (failure) => Left(failure),
      (currentSettings) async {
        final updatedSettings = currentSettings.copyWith(
          darkModeEnabled: enabled,
          updatedAt: DateTime.now().toUtc(),
        );
        
        return await updateAppSettings(updatedSettings);
      },
    );
  }

  @override
  Future<Either<Failure, AppSettings>> updateLanguage(String language) async {
    final currentSettingsResult = await getAppSettings();
    
    return currentSettingsResult.fold(
      (failure) => Left(failure),
      (currentSettings) async {
        final updatedSettings = currentSettings.copyWith(
          language: language,
          updatedAt: DateTime.now().toUtc(),
        );
        
        return await updateAppSettings(updatedSettings);
      },
    );
  }

  @override
  Future<Either<Failure, AppSettings>> updateCurrency(String currency) async {
    final currentSettingsResult = await getAppSettings();
    
    return currentSettingsResult.fold(
      (failure) => Left(failure),
      (currentSettings) async {
        final updatedSettings = currentSettings.copyWith(
          currency: currency,
          updatedAt: DateTime.now().toUtc(),
        );
        
        return await updateAppSettings(updatedSettings);
      },
    );
  }

  @override
  Future<Either<Failure, AppSettings>> updateAdditionalSetting(String key, dynamic value) async {
    final currentSettingsResult = await getAppSettings();
    
    return currentSettingsResult.fold(
      (failure) => Left(failure),
      (currentSettings) async {
        final currentAdditional = currentSettings.additionalSettings ?? {};
        final updatedAdditional = {...currentAdditional, key: value};
        
        final updatedSettings = currentSettings.copyWith(
          additionalSettings: updatedAdditional,
          updatedAt: DateTime.now().toUtc(),
        );
        
        return await updateAppSettings(updatedSettings);
      },
    );
  }

  @override
  Future<Either<Failure, AppSettings>> removeAdditionalSetting(String key) async {
    final currentSettingsResult = await getAppSettings();
    
    return currentSettingsResult.fold(
      (failure) => Left(failure),
      (currentSettings) async {
        final currentAdditional = currentSettings.additionalSettings ?? {};
        final updatedAdditional = Map<String, dynamic>.from(currentAdditional);
        updatedAdditional.remove(key);
        
        final updatedSettings = currentSettings.copyWith(
          additionalSettings: updatedAdditional,
          updatedAt: DateTime.now().toUtc(),
        );
        
        return await updateAppSettings(updatedSettings);
      },
    );
  }

  @override
  Future<Either<Failure, AppSettings>> resetToDefaults() async {
    try {
      final defaultSettings = AppSettings.defaultSettings();
      return await updateAppSettings(defaultSettings);
    } catch (e) {
      return Left(Failure.unexpected(message: 'Failed to reset settings to defaults: $e'));
    }
  }

  @override
  Future<Either<Failure, Map<String, dynamic>>> exportSettings() async {
    final settingsResult = await getAppSettings();
    
    return settingsResult.fold(
      (failure) => Left(failure),
      (settings) async {
        try {
          final backupSettingsResult = await getBackupSettings();
          final dateRangeSettingsResult = await getDateRangeSettings();
          
          return Right({
            'appSettings': settings.toJson(),
            'backupSettings': backupSettingsResult.getOrElse(() => BackupSettings.defaultSettings()).toJson(),
            'dateRangeSettings': dateRangeSettingsResult.getOrElse(() => DateRangeSettings.currentMonth()).toJson(),
            'exportedAt': DateTime.now().toIso8601String(),
          });
        } catch (e) {
          return Left(Failure.unexpected(message: 'Failed to export settings: $e'));
        }
      },
    );
  }

  @override
  Future<Either<Failure, AppSettings>> importSettings(Map<String, dynamic> settingsJson) async {
    try {
      final appSettingsJson = settingsJson['appSettings'] as Map<String, dynamic>?;
      if (appSettingsJson == null) {
        return Left(Failure.validation(message: 'Invalid settings format'));
      }

      final settings = AppSettings.fromJson(appSettingsJson);
      return await updateAppSettings(settings);
    } catch (e) {
      return Left(Failure.unexpected(message: 'Failed to import settings: $e'));
    }
  }

  @override
  Future<Either<Failure, bool>> validateSettings(AppSettings settings) async {
    try {
      // Validate date range
      if (settings.dateRangeStart.isAfter(settings.dateRangeEnd)) {
        return const Right(false);
      }

      // Validate language code format
      if (!RegExp(r'^[a-z]{2}(-[A-Z]{2})?$').hasMatch(settings.language)) {
        return const Right(false);
      }

      // Validate currency code format
      if (!RegExp(r'^[A-Z]{3}$').hasMatch(settings.currency)) {
        return const Right(false);
      }

      return const Right(true);
    } catch (e) {
      return Left(Failure.unexpected(message: 'Failed to validate settings: $e'));
    }
  }

  @override
  Stream<Either<Failure, AppSettings>> get settingsStream {
    _settingsController ??= StreamController<Either<Failure, AppSettings>>.broadcast();
    return _settingsController!.stream;
  }

  void _setupSettingsStream() {
    _settingsController ??= StreamController<Either<Failure, AppSettings>>.broadcast();
  }

  void dispose() {
    _settingsController?.close();
  }
}
