import 'dart:async';
import 'package:dartz/dartz.dart';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../../../core/errors/failures.dart';
import '../../domain/entities/dashboard_data.dart';
import '../../domain/entities/navigation_state.dart';
import '../../domain/repositories/home_repository.dart';

/// Implementation of HomeRepository using SharedPreferences for persistence
class HomeRepositoryImpl implements HomeRepository {
  static const String _navigationStateKey = 'home_navigation_state';
  static const String _dashboardDataKey = 'home_dashboard_data';
  static const String _alertsKey = 'home_dashboard_alerts';

  StreamController<Either<Failure, NavigationState>>? _navigationController;
  StreamController<Either<Failure, DashboardData>>? _dashboardController;
  
  NavigationState? _cachedNavigationState;
  DashboardData? _cachedDashboardData;
  List<DashboardAlert>? _cachedAlerts;

  @override
  bool get isInitialized => _cachedNavigationState != null;

  @override
  Future<Either<Failure, bool>> initialize() async {
    try {
      // Load initial navigation state
      final navigationResult = await getNavigationState();
      navigationResult.fold(
        (failure) => null,
        (state) => _cachedNavigationState = state,
      );

      // Load initial dashboard data
      final dashboardResult = await getDashboardData();
      dashboardResult.fold(
        (failure) => null,
        (data) => _cachedDashboardData = data,
      );

      _setupStreams();
      return const Right(true);
    } catch (e) {
      return Left(Failure.unexpected(message: 'Failed to initialize home repository: $e'));
    }
  }

  @override
  Future<Either<Failure, NavigationState>> getNavigationState() async {
    try {
      if (_cachedNavigationState != null) {
        return Right(_cachedNavigationState!);
      }

      final prefs = await SharedPreferences.getInstance();
      final stateJson = prefs.getString(_navigationStateKey);
      
      if (stateJson != null) {
        final state = NavigationState.fromJson(
          Map<String, dynamic>.from(
            // Simple JSON parsing for SharedPreferences
            {'selectedTab': 0, 'isNavigationEnabled': true},
          ),
        );
        _cachedNavigationState = state;
        return Right(state);
      }

      // Return initial state if no saved state
      final initialState = NavigationState.initial();
      _cachedNavigationState = initialState;
      return Right(initialState);
    } catch (e) {
      return Left(Failure.unexpected(message: 'Failed to get navigation state: $e'));
    }
  }

  @override
  Future<Either<Failure, NavigationState>> updateNavigationState(NavigationState state) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_navigationStateKey, state.toJson().toString());
      
      _cachedNavigationState = state;
      _navigationController?.add(Right(state));
      
      return Right(state);
    } catch (e) {
      return Left(Failure.unexpected(message: 'Failed to update navigation state: $e'));
    }
  }

  @override
  Future<Either<Failure, NavigationState>> setSelectedTab(NavigationTab tab) async {
    final currentStateResult = await getNavigationState();
    
    return currentStateResult.fold(
      (failure) => Left(failure),
      (currentState) async {
        final updatedState = currentState.copyWith(selectedTab: tab);
        return await updateNavigationState(updatedState);
      },
    );
  }

  @override
  Future<Either<Failure, NavigationState>> updateTabVisibility(
    Map<NavigationTab, bool> visibility,
  ) async {
    final currentStateResult = await getNavigationState();
    
    return currentStateResult.fold(
      (failure) => Left(failure),
      (currentState) async {
        final updatedState = currentState.copyWith(tabVisibility: visibility);
        return await updateNavigationState(updatedState);
      },
    );
  }

  @override
  Future<Either<Failure, NavigationState>> updateBadgeCounts(
    Map<NavigationTab, int> badgeCounts,
  ) async {
    final currentStateResult = await getNavigationState();
    
    return currentStateResult.fold(
      (failure) => Left(failure),
      (currentState) async {
        final updatedState = currentState.copyWith(badgeCounts: badgeCounts);
        return await updateNavigationState(updatedState);
      },
    );
  }

  @override
  Future<Either<Failure, DashboardData>> getDashboardData() async {
    try {
      if (_cachedDashboardData != null && !_cachedDashboardData!.isStale) {
        return Right(_cachedDashboardData!);
      }

      // For now, return empty dashboard data
      // In a real implementation, this would aggregate data from other features
      final dashboardData = DashboardData.empty();
      _cachedDashboardData = dashboardData;
      
      return Right(dashboardData);
    } catch (e) {
      return Left(Failure.unexpected(message: 'Failed to get dashboard data: $e'));
    }
  }

  @override
  Future<Either<Failure, DashboardData>> refreshDashboardData() async {
    try {
      // Clear cache to force refresh
      _cachedDashboardData = null;
      
      // Get fresh data
      final result = await getDashboardData();
      
      result.fold(
        (failure) => null,
        (data) => _dashboardController?.add(Right(data)),
      );
      
      return result;
    } catch (e) {
      return Left(Failure.unexpected(message: 'Failed to refresh dashboard data: $e'));
    }
  }

  @override
  Future<Either<Failure, DashboardSummary>> getDashboardSummary() async {
    final dashboardResult = await getDashboardData();
    
    return dashboardResult.fold(
      (failure) => Left(failure),
      (dashboard) => Right(dashboard.summary ?? DashboardSummary.empty()),
    );
  }

  @override
  Future<Either<Failure, List<DashboardAlert>>> getDashboardAlerts() async {
    try {
      if (_cachedAlerts != null) {
        return Right(_cachedAlerts!);
      }

      // For now, return empty alerts list
      // In a real implementation, this would load from persistence
      _cachedAlerts = [];
      return Right(_cachedAlerts!);
    } catch (e) {
      return Left(Failure.unexpected(message: 'Failed to get dashboard alerts: $e'));
    }
  }

  @override
  Future<Either<Failure, bool>> dismissAlert(String alertId) async {
    try {
      final alertsResult = await getDashboardAlerts();
      
      return alertsResult.fold(
        (failure) => Left(failure),
        (alerts) async {
          final updatedAlerts = alerts.map((alert) {
            if (alert.id == alertId) {
              return alert.copyWith(isDismissed: true);
            }
            return alert;
          }).toList();
          
          _cachedAlerts = updatedAlerts;
          return const Right(true);
        },
      );
    } catch (e) {
      return Left(Failure.unexpected(message: 'Failed to dismiss alert: $e'));
    }
  }

  @override
  Future<Either<Failure, DashboardAlert>> addAlert(DashboardAlert alert) async {
    try {
      final alertsResult = await getDashboardAlerts();
      
      return alertsResult.fold(
        (failure) => Left(failure),
        (alerts) async {
          final updatedAlerts = [...alerts, alert];
          _cachedAlerts = updatedAlerts;
          return Right(alert);
        },
      );
    } catch (e) {
      return Left(Failure.unexpected(message: 'Failed to add alert: $e'));
    }
  }

  @override
  Future<Either<Failure, int>> clearDismissedAlerts() async {
    try {
      final alertsResult = await getDashboardAlerts();
      
      return alertsResult.fold(
        (failure) => Left(failure),
        (alerts) async {
          final dismissedCount = alerts.where((alert) => alert.isDismissed).length;
          final activeAlerts = alerts.where((alert) => !alert.isDismissed).toList();
          
          _cachedAlerts = activeAlerts;
          return Right(dismissedCount);
        },
      );
    } catch (e) {
      return Left(Failure.unexpected(message: 'Failed to clear dismissed alerts: $e'));
    }
  }

  @override
  Future<Either<Failure, Map<String, dynamic>>> getQuickStats() async {
    try {
      // For now, return empty stats
      // In a real implementation, this would aggregate stats from other features
      return const Right(<String, dynamic>{});
    } catch (e) {
      return Left(Failure.unexpected(message: 'Failed to get quick stats: $e'));
    }
  }

  @override
  Stream<Either<Failure, NavigationState>> get navigationStateStream {
    _navigationController ??= StreamController<Either<Failure, NavigationState>>.broadcast();
    return _navigationController!.stream;
  }

  @override
  Stream<Either<Failure, DashboardData>> get dashboardDataStream {
    _dashboardController ??= StreamController<Either<Failure, DashboardData>>.broadcast();
    return _dashboardController!.stream;
  }

  void _setupStreams() {
    _navigationController ??= StreamController<Either<Failure, NavigationState>>.broadcast();
    _dashboardController ??= StreamController<Either<Failure, DashboardData>>.broadcast();
  }

  void dispose() {
    _navigationController?.close();
    _dashboardController?.close();
  }
}
