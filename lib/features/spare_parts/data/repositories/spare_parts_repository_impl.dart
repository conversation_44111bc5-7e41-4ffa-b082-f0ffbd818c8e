import 'package:dartz/dartz.dart';
import 'package:drift/drift.dart' as db;
import 'package:flutter/foundation.dart';
import 'package:uuid/uuid.dart';

import '../../../../core/datasources/app_database.dart' as db;
import '../../../../core/datasources/converters/sync_status_converter.dart';
import '../../../../core/errors/exceptions.dart';
import '../../../../core/errors/failures.dart';
import '../../../../core/repositories/base_repository.dart';
import '../../../../core/services/sync/sync_service.dart';
import '../../domain/entities/replacement_history.dart';
import '../../domain/entities/spare_part.dart';
import '../../domain/repositories/spare_parts_repository.dart';

class EnhancedSparePartsRepositoryImpl
    extends
        BaseRepository<EnhancedSparePart, db.SparePart, db.SparePartsCompanion>
    implements EnhancedSparePartsRepository {
  EnhancedSparePartsRepositoryImpl({
    required db.AppDatabase database,
    required SyncService syncService,
  }) : super(database: database, syncService: syncService);

  // Implement abstract methods from BaseRepository

  @override
  db.SparePartsCompanion mapToCompanion(EnhancedSparePart entity) {
    return db.SparePartsCompanion(
      id: entity.id != null ? db.Value(entity.id!) : const db.Value.absent(),
      uuid: db.Value(const Uuid().v4()),
      partName: db.Value(entity.partName),
      partType: db.Value(entity.partType),
      price: db.Value(entity.price),
      mileageLimit: db.Value(entity.mileageLimit),
      initialMileage: db.Value(entity.initialMileage),
      installationDate: db.Value(entity.installationDate),
      currentMileage: db.Value(entity.currentMileage),
      warningStatus: db.Value(entity.warningStatus),
      replacementCount: db.Value(entity.replacementCount),
      notes: db.Value(entity.notes),
      createdAt: db.Value(entity.createdAt),
      updatedAt: db.Value(DateTime.now()),
      syncStatus: const db.Value(SyncStatus.pendingUpload),
    );
  }

  @override
  EnhancedSparePart mapFromData(db.SparePart data) {
    return EnhancedSparePart.withComputedValues(
      id: data.id,
      partName: data.partName,
      partType: data.partType,
      price: data.price,
      mileageLimit: data.mileageLimit,
      initialMileage: data.initialMileage,
      installationDate: data.installationDate,
      currentMileage: data.currentMileage,
      warningStatus: data.warningStatus,
      replacementCount: data.replacementCount,
      notes: data.notes,
      createdAt: data.createdAt,
      updatedAt: data.updatedAt,
    );
  }

  @override
  Future<int> insertEntity(db.SparePartsCompanion companion) async {
    return database.into(database.spareParts).insert(companion);
  }

  @override
  Future<bool> updateEntity(EnhancedSparePart entity) async {
    final affectedRows = await (database.update(
      database.spareParts,
    )..where((tbl) => tbl.id.equals(entity.id!))).write(mapToCompanion(entity));
    return affectedRows > 0;
  }

  @override
  Future<bool> deleteEntity(int id) async {
    // Use soft delete instead of hard delete
    final affectedRows =
        await (database.update(
          database.spareParts,
        )..where((tbl) => tbl.id.equals(id))).write(
          db.SparePartsCompanion(
            deletedAt: db.Value(DateTime.now()),
            syncStatus: const db.Value(SyncStatus.pendingUpload),
          ),
        );
    return affectedRows > 0;
  }

  @override
  Future<List<db.SparePart>> getAllData() async {
    return database.getAllSpareParts();
  }

  @override
  Future<db.SparePart?> getDataById(int id) async {
    final query = database.select(database.spareParts)
      ..where((tbl) => tbl.id.equals(id));
    return query.getSingleOrNull();
  }

  @override
  Future<List<db.SparePart>> getUnsyncedData() async {
    return database.getUnsyncedSpareParts();
  }

  @override
  Future<void> markDataAsSynced(String uuid) async {
    await database.markSparePartAsSynced(uuid);
  }

  // Implement EnhancedSparePartsRepository specific methods

  @override
  Future<Either<Failure, List<EnhancedSparePart>>> getAllSpareParts() async {
    return getAll(); // Use the inherited getAll method from BaseRepository
  }

  @override
  Future<Either<Failure, EnhancedSparePart>> getSparePartById(int id) async {
    return getById(id); // Use the inherited getById method from BaseRepository
  }

  @override
  Future<Either<Failure, EnhancedSparePart>> saveSparePart(
    EnhancedSparePart sparePart,
  ) async {
    return save(sparePart); // Use the inherited save method from BaseRepository
  }

  @override
  Future<Either<Failure, EnhancedSparePart>> updateSparePart(
    EnhancedSparePart sparePart,
  ) async {
    return update(
      sparePart,
    ); // Use the inherited update method from BaseRepository
  }

  @override
  Future<Either<Failure, bool>> deleteSparePart(int id) async {
    return delete(id); // Use the inherited delete method from BaseRepository
  }

  @override
  Future<Either<Failure, List<EnhancedReplacementHistory>>>
  getReplacementHistoryForPart(int sparePartId) async {
    try {
      // Get all history entries for this part
      final historyList = await database.getHistoryForSparePart(sparePartId);

      // Debug logging
      debugPrint('Fetching history for spare part ID: $sparePartId');
      debugPrint('History entries found: ${historyList.length}');

      if (historyList.isNotEmpty) {
        debugPrint('First history entry: ${historyList.first.toString()}');
      }

      if (historyList.isEmpty) {
        return const Right([]);
      }

      // Sort by date, oldest first (for processing)
      historyList.sort(
        (a, b) => a.replacementDate.compareTo(b.replacementDate),
      );

      // Create a list to hold our enhanced history entries
      final enhancedHistoryList = <EnhancedReplacementHistory>[];

      // Process each history entry
      for (int i = 0; i < historyList.length; i++) {
        final currentHistory = historyList[i];

        // With our updated database schema, we can now directly use the installation date
        // and initial mileage from the history record itself

        // Create enhanced history entry with all the fields from the database
        final enhancedHistory = EnhancedReplacementHistory.withComputedValues(
          id: currentHistory.id,
          partName: currentHistory.partName,
          partType: currentHistory.partType,
          price: currentHistory.price,
          replacementDate: currentHistory.replacementDate,
          mileageAtReplacement: currentHistory.mileageAtReplacement,
          sparePartId: currentHistory.sparePartId,
          // Use the actual data from the database
          installationDate: currentHistory.installationDate,
          initialMileage: currentHistory.initialMileage,
          // New fields
          replacementReason: currentHistory.replacementReason,
          replacedByPartId: currentHistory.replacedByPartId,
          replacementCount: currentHistory.replacementCount,
          notes: currentHistory.notes,
          createdAt: currentHistory.createdAt,
        );

        enhancedHistoryList.add(enhancedHistory);
      }

      // Sort by date, most recent first (for display)
      enhancedHistoryList.sort(
        (a, b) => b.replacementDate.compareTo(a.replacementDate),
      );

      // Debug logging
      debugPrint(
        'Enhanced history entries created: ${enhancedHistoryList.length}',
      );

      return Right(enhancedHistoryList);
    } on DatabaseException catch (e) {
      debugPrint(
        'Database exception in getReplacementHistoryForPart: ${e.message}',
      );
      return Left(Failure.database(message: e.message));
    } catch (e) {
      debugPrint('Unexpected error in getReplacementHistoryForPart: $e');
      return Left(Failure.unexpected(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, EnhancedReplacementHistory>> saveReplacementHistory({
    required String partName,
    required String partType,
    required double price,
    required DateTime replacementDate,
    required int mileageAtReplacement,
    required int sparePartId,
    required DateTime installationDate,
    required int initialMileage,
    String replacementReason = 'Regular maintenance',
    int? replacedByPartId,
    int replacementCount = 1,
    String notes = '',
  }) async {
    try {
      // Calculate usage statistics
      final usageDays = replacementDate.difference(installationDate).inDays;
      final usageMileage = mileageAtReplacement - initialMileage;

      // Create the companion object with all the new fields
      final companion = db.SparePartsHistoryCompanion(
        partName: db.Value(partName),
        partType: db.Value(partType),
        price: db.Value(price),
        replacementDate: db.Value(replacementDate),
        mileageAtReplacement: db.Value(mileageAtReplacement),
        sparePartId: db.Value(sparePartId),
        // New fields
        installationDate: db.Value(installationDate),
        initialMileage: db.Value(initialMileage),
        replacementReason: db.Value(replacementReason),
        replacedByPartId: replacedByPartId != null
            ? db.Value(replacedByPartId)
            : const db.Value.absent(),
        replacementCount: db.Value(replacementCount),
        usageDays: db.Value(usageDays),
        usageMileage: db.Value(usageMileage),
        notes: db.Value(notes),
        createdAt: db.Value(DateTime.now()),
      );

      final id = await database.insertSparePartHistory(companion);

      // Create enhanced history entry with all the new fields
      final enhancedHistory = EnhancedReplacementHistory.withComputedValues(
        id: id,
        partName: partName,
        partType: partType,
        price: price,
        replacementDate: replacementDate,
        mileageAtReplacement: mileageAtReplacement,
        sparePartId: sparePartId,
        installationDate: installationDate,
        initialMileage: initialMileage,
        replacementReason: replacementReason,
        replacedByPartId: replacedByPartId,
        replacementCount: replacementCount,
        notes: notes,
        createdAt: DateTime.now(),
      );

      return Right(enhancedHistory);
    } on DatabaseException catch (e) {
      return Left(Failure.database(message: e.message));
    } catch (e) {
      return Left(Failure.unexpected(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, int>> getHighestMileage() async {
    try {
      // Use a custom query to get the maximum final_mileage value
      final result = await database
          .customSelect('SELECT MAX(final_mileage) as maxMileage FROM income')
          .getSingleOrNull();

      if (result == null || result.data['maxMileage'] == null) {
        return const Right(0); // Default to 0 if no records found
      }

      final maxMileage = result.data['maxMileage'] as int;
      return Right(maxMileage);
    } on DatabaseException catch (e) {
      return Left(Failure.database(message: e.message));
    } catch (e) {
      return Left(Failure.unexpected(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, bool>> updateAllPartsMileage(int latestMileage) async {
    try {
      await database.updateSparePartsMileage(latestMileage);
      return const Right(true);
    } on DatabaseException catch (e) {
      return Left(Failure.database(message: e.message));
    } catch (e) {
      return Left(Failure.unexpected(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, ReplacementResult>> replaceSparePart({
    required EnhancedSparePart oldPart,
    required DateTime replacementDate,
    required int currentMileage,
    required double newPartPrice,
    String? newPartName,
    String? newPartType,
    int? newMileageLimit,
    String replacementReason = 'Regular maintenance',
    String notes = '',
  }) async {
    try {
      if (oldPart.id == null) {
        return const Left(
          Failure.invalidInput(
            message: 'Spare part ID cannot be null for replacement',
          ),
        );
      }

      // 1. Save the replacement history with all the new fields
      final historyResult = await saveReplacementHistory(
        partName: oldPart.partName,
        partType: oldPart.partType,
        price: oldPart.price,
        replacementDate: replacementDate,
        mileageAtReplacement: currentMileage,
        sparePartId: oldPart.id!,
        installationDate: oldPart.installationDate,
        initialMileage: oldPart.initialMileage,
        replacementReason: replacementReason, // Use the provided reason
        replacedByPartId: null, // Will be updated after creating the new part
        replacementCount:
            oldPart.replacementCount + 1, // Increment the replacement count
        notes: oldPart.notes, // Use the notes from the old part being replaced
      );

      return historyResult.fold((failure) => Left(failure), (
        savedHistory,
      ) async {
        // 2. Create a new spare part with the updated information
        // For the new part, the installation date is the replacement date
        // and the initial mileage is the current mileage
        final updatedPart = EnhancedSparePart.withComputedValues(
          id: oldPart.id,
          partName: newPartName ?? oldPart.partName,
          partType: newPartType ?? oldPart.partType,
          price: newPartPrice,
          mileageLimit: newMileageLimit ?? oldPart.mileageLimit,
          initialMileage: currentMileage,
          installationDate: replacementDate,
          currentMileage: currentMileage,
          warningStatus: false,
          // New fields
          replacementCount:
              oldPart.replacementCount + 1, // Increment the replacement count
          notes: notes, // Use the provided notes
          createdAt: oldPart.createdAt, // Keep the original creation date
          updatedAt: DateTime.now(), // Update the timestamp
        );

        // 3. Save the updated spare part
        // We're using updateSparePart which will preserve the UUID
        final partResult = await updateSparePart(updatedPart);

        return partResult.fold(
          (failure) => Left(failure),
          (updatedSparePart) => Right(
            ReplacementResult(
              sparePart: updatedSparePart,
              replacementHistory: savedHistory,
            ),
          ),
        );
      });
    } catch (e) {
      return Left(
        Failure.businessLogic(message: 'Error replacing spare part: $e'),
      );
    }
  }
}
