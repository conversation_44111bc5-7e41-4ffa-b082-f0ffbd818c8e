import 'package:dartz/dartz.dart' hide Order;
import 'package:drift/drift.dart';
import 'package:uuid/uuid.dart';

import '../../../../core/datasources/app_database.dart' as db;
import '../../../../core/datasources/converters/sync_status_converter.dart';
import '../../../../core/errors/exceptions.dart';
import '../../../../core/errors/failures.dart';
import '../../../../core/repositories/base_repository.dart';
import '../../../../core/services/sync/sync_service.dart';
import '../../domain/entities/order.dart' as domain;
import '../../domain/repositories/order_repository.dart';
import '../../domain/use_cases/calculate_bid_acceptance.dart';

// Re-export aliases to make mapping easier
const int missingId = -1;

class OrderRepositoryImpl
    extends BaseDateRangeRepository<domain.Order, db.Order, db.OrdersCompanion>
    implements OrderRepository {
  final CalculateBidAcceptance calculateBidAcceptance;

  OrderRepositoryImpl({
    required db.AppDatabase database,
    required this.calculateBidAcceptance,
    required SyncService syncService,
  }) : super(database: database, syncService: syncService);

  // Implement abstract methods from BaseRepository

  @override
  db.OrdersCompanion mapToCompanion(domain.Order entity) {
    return db.OrdersCompanion(
      id: entity.id != null ? Value(entity.id!) : const Value.absent(),
      uuid: Value(const Uuid().v4()),
      date: Value(entity.date),
      orderCompleted: Value(entity.orderCompleted),
      orderMissed: Value(entity.orderMissed),
      orderCanceled: Value(entity.orderCanceled),
      cbsOrder: Value(entity.cbsOrder),
      incomingOrder: Value(entity.incomingOrder),
      orderReceived: Value(entity.orderReceived),
      bidAcceptance: entity.bidAcceptance != null
          ? Value(entity.bidAcceptance!)
          : const Value.absent(),
      tripCompletion: entity.tripCompletion != null
          ? Value(entity.tripCompletion!)
          : const Value.absent(),
      points: Value(entity.points),
      trip: Value(entity.trip),
      bonus: Value(entity.bonus),
      tips: Value(entity.tips),
      income: entity.income != null
          ? Value(entity.income!)
          : const Value.absent(),
      createdAt: Value(DateTime.now()),
      updatedAt: Value(DateTime.now()),
      syncStatus: const Value(SyncStatus.pendingUpload),
    );
  }

  @override
  domain.Order mapFromData(db.Order data) {
    return domain.Order(
      id: data.id,
      date: data.date,
      orderCompleted: data.orderCompleted,
      orderMissed: data.orderMissed,
      orderCanceled: data.orderCanceled,
      cbsOrder: data.cbsOrder,
      incomingOrder: data.incomingOrder,
      orderReceived: data.orderReceived,
      bidAcceptance: data.bidAcceptance,
      tripCompletion: data.tripCompletion,
      points: data.points,
      trip: data.trip,
      bonus: data.bonus,
      tips: data.tips,
      income: data.income,
    );
  }

  @override
  Future<int> insertEntity(db.OrdersCompanion companion) async {
    return database.into(database.orders).insert(companion);
  }

  @override
  Future<bool> updateEntity(domain.Order entity) async {
    final affectedRows = await (database.update(
      database.orders,
    )..where((tbl) => tbl.id.equals(entity.id!))).write(mapToCompanion(entity));
    return affectedRows > 0;
  }

  @override
  Future<bool> deleteEntity(int id) async {
    // Use soft delete instead of hard delete
    final affectedRows =
        await (database.update(
          database.orders,
        )..where((tbl) => tbl.id.equals(id))).write(
          db.OrdersCompanion(
            deletedAt: Value(DateTime.now()),
            syncStatus: const Value(SyncStatus.pendingUpload),
          ),
        );
    return affectedRows > 0;
  }

  @override
  Future<List<db.Order>> getAllData() async {
    return database.getAllOrders();
  }

  @override
  Future<db.Order?> getDataById(int id) async {
    final query = database.select(database.orders)
      ..where((tbl) => tbl.id.equals(id));
    return query.getSingleOrNull();
  }

  @override
  Future<List<db.Order>> getUnsyncedData() async {
    return database.getUnsyncedOrders();
  }

  @override
  Future<void> markDataAsSynced(String uuid) async {
    await database.markOrderAsSynced(uuid);
  }

  domain.Order _copyEntityWithId(domain.Order entity, int id) {
    return entity.copyWith(id: id);
  }

  // Implement BaseDateRangeRepository abstract methods

  @override
  Future<List<db.Order>> getDataForDateRange(
    DateTime start,
    DateTime end,
  ) async {
    final query = database.select(database.orders)
      ..where((tbl) => tbl.deletedAt.isNull())
      ..where((tbl) => tbl.date.isBetweenValues(start, end))
      ..orderBy([(t) => OrderingTerm.desc(t.date)]);
    return query.get();
  }

  @override
  Future<bool> checkDataDateExists(DateTime date, {int? excludeId}) async {
    var query = database.select(database.orders)
      ..where((tbl) => tbl.deletedAt.isNull())
      ..where((tbl) => tbl.date.equals(date));

    if (excludeId != null) {
      query = query..where((tbl) => tbl.id.equals(excludeId).not());
    }

    final result = await query.getSingleOrNull();
    return result != null;
  }

  // Override save method to include calculation logic
  @override
  Future<Either<Failure, domain.Order>> save(domain.Order entity) async {
    return executeWithErrorHandling<int, domain.Order>(() async {
      // Calculate all derived fields using the calculation service
      final calculatedOrderResult = calculateBidAcceptance.execute(entity);

      return calculatedOrderResult.fold(
        (failure) => throw Exception(failure.message),
        (calculatedOrder) async {
          final companion = mapToCompanion(calculatedOrder);
          final id = await insertEntity(companion);
          return id;
        },
      );
    }, (id) => _copyEntityWithId(entity, id));
  }

  // Override update method to include calculation logic
  @override
  Future<Either<Failure, domain.Order>> update(domain.Order entity) async {
    return executeWithErrorHandling<bool, domain.Order>(() async {
      if (entity.id == null) {
        throw Exception('Order ID cannot be null for update');
      }

      // Calculate all derived fields using the calculation service
      final calculatedOrderResult = calculateBidAcceptance.execute(entity);

      return calculatedOrderResult.fold(
        (failure) => throw Exception(failure.message),
        (calculatedOrder) async {
          final success = await updateEntity(calculatedOrder);
          if (!success) {
            throw Exception('Update failed');
          }
          return success;
        },
      );
    }, (success) => entity);
  }

  // Implement OrderRepository specific methods

  @override
  Future<Either<Failure, List<domain.Order>>> getAllOrders() async {
    return getAll(); // Use the inherited getAll method from BaseRepository
  }

  @override
  Future<Either<Failure, domain.Order>> getOrderById(int id) async {
    return getById(id); // Use the inherited getById method from BaseRepository
  }

  // Implement order-specific methods that are not handled by BaseRepository

  @override
  Future<Either<Failure, domain.Order>> saveOrder(domain.Order order) async {
    return save(order); // Use the inherited save method from BaseRepository
  }

  @override
  Future<Either<Failure, domain.Order>> updateOrder(domain.Order order) async {
    return update(order); // Use the inherited update method from BaseRepository
  }

  @override
  Future<Either<Failure, bool>> deleteOrder(int id) async {
    return delete(id); // Use the inherited delete method from BaseRepository
  }

  @override
  Future<Either<Failure, List<domain.Order>>> getOrdersForDateRange(
    DateTime start,
    DateTime end,
  ) async {
    // Adjust end date to include the full day
    final adjustedEnd = end
        .add(const Duration(days: 1))
        .subtract(const Duration(microseconds: 1));
    return getForDateRange(
      start,
      adjustedEnd,
    ); // Use the inherited method from BaseDateRangeRepository
  }

  @override
  Future<Either<Failure, List<domain.Order>>>
  getOrdersForPerformanceCalculation(DateTime endDate) async {
    // Calculate the date range for the last 14 days (not including the current date)
    // If endDate is March 25, 2025, we want orders from March 11, 2025 to March 24, 2025
    final adjustedEndDate = endDate.subtract(
      const Duration(days: 1),
    ); // Exclude current date
    final startDate = adjustedEndDate.subtract(
      const Duration(days: 13),
    ); // 14 days total

    return getOrdersForDateRange(
      startDate,
      adjustedEndDate,
    ); // Use the method we just implemented
  }

  @override
  Future<Either<Failure, int>> getTotalCompletedOrdersForLast14Days(
    DateTime endDate,
  ) async {
    try {
      // Get orders for the last 14 days
      final ordersResult = await getOrdersForPerformanceCalculation(endDate);

      return ordersResult.fold((failure) => Left(failure), (orders) {
        // Calculate total completed orders
        int totalOrdersCompleted = 0;
        for (final order in orders) {
          totalOrdersCompleted += order.orderCompleted;
        }

        return Right(totalOrdersCompleted);
      });
    } catch (e) {
      return Left(
        Failure.unexpected(
          message: 'Error calculating total completed orders: $e',
        ),
      );
    }
  }

  @override
  Future<Either<Failure, int>> getTotalPointsForMonth(
    int year,
    int month,
  ) async {
    try {
      final startDate = DateTime(year, month, 1);
      final endDate = DateTime(year, month + 1, 0); // Last day of the month

      final result = await getOrdersForDateRange(startDate, endDate);

      return result.fold((failure) => Left(failure), (orders) {
        int totalPoints = 0;
        for (final order in orders) {
          totalPoints += order.points.toInt();
        }
        return Right(totalPoints);
      });
    } catch (e) {
      return Left(Failure.unexpected(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, bool>> checkDateExists(
    DateTime date, {
    int? excludeId,
  }) async {
    try {
      // Create a query to find records with the same date
      final query = database.select(database.orders)
        ..where((tbl) => tbl.date.equals(date))
        ..where((tbl) => tbl.deletedAt.isNull());

      // If excludeId is provided, exclude that record from the check
      // This is useful when updating an existing record
      if (excludeId != null) {
        query.where((tbl) => tbl.id.isNotValue(excludeId));
      }

      // Get the count of records with the same date
      final count = await query.get().then((records) => records.length);

      // Return true if any records were found with the same date
      return Right(count > 0);
    } on DatabaseException catch (e) {
      return Left(Failure.database(message: e.message));
    } catch (e) {
      return Left(Failure.unexpected(message: e.toString()));
    }
  }
}
