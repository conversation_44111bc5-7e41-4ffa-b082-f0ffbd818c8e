import 'package:drift/drift.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:uuid/uuid.dart';

import '../../../../core/datasources/app_database.dart';
import '../../../../core/datasources/converters/sync_status_converter.dart';
import '../../../../core/providers/app_settings_provider.dart';

import '../../../../core/services/sync/sync_operations.dart';
import '../../../../core/services/sync/sync_service.dart';

// Provider for Orders Repository
final ordersRepositoryProvider = Provider<OrdersRepositoryImpl>((ref) {
  final database = ref.watch(databaseProvider);
  final syncService = ref.watch(syncServiceProvider);
  return OrdersRepositoryImpl(database, syncService);
});

// Implementation of Orders Repository
class OrdersRepositoryImpl {
  final AppDatabase _db;
  final SyncService _syncService;

  OrdersRepositoryImpl(this._db, this._syncService);

  Future<int> add(Order entity) async {
    final id = await _db
        .into(_db.orders)
        .insert(
          OrdersCompanion.insert(
            uuid: Value(const Uuid().v4()),
            date: entity.date,
            orderCompleted: entity.orderCompleted,
            orderMissed: entity.orderMissed,
            orderCanceled: entity.orderCanceled,
            cbsOrder: entity.cbsOrder,
            incomingOrder: Value(entity.incomingOrder),
            orderReceived: Value(entity.orderReceived),
            bidAcceptance: Value(entity.bidAcceptance),
            tripCompletion: Value(entity.tripCompletion),
            points: entity.points,
            trip: entity.trip,
            bonus: entity.bonus,
            tips: entity.tips,
            income: Value(entity.income),
            syncStatus: const Value(SyncStatus.pendingUpload),
          ),
        );

    // Trigger sync after adding a record
    _triggerSync();

    return id;
  }

  Future<bool> update(Order entity) async {
    // Check if ID is present
    if (entity.id <= 0) {
      return false;
    }

    final affectedRows =
        await (_db.update(
          _db.orders,
        )..where((tbl) => tbl.id.equals(entity.id))).write(
          OrdersCompanion(
            id: Value(entity.id),
            uuid: Value(entity.uuid),
            date: Value(entity.date),
            orderCompleted: Value(entity.orderCompleted),
            orderMissed: Value(entity.orderMissed),
            orderCanceled: Value(entity.orderCanceled),
            cbsOrder: Value(entity.cbsOrder),
            incomingOrder: Value(entity.incomingOrder),
            orderReceived: Value(entity.orderReceived),
            bidAcceptance: Value(entity.bidAcceptance),
            tripCompletion: Value(entity.tripCompletion),
            points: Value(entity.points),
            trip: Value(entity.trip),
            bonus: Value(entity.bonus),
            tips: Value(entity.tips),
            income: Value(entity.income),
            updatedAt: Value(DateTime.now()),
            syncStatus: const Value(SyncStatus.pendingUpload),
          ),
        );

    final result = affectedRows > 0;

    // Trigger sync after updating a record
    _triggerSync();

    return result;
  }

  Future<bool> delete(int id) async {
    // Use soft delete that marks for sync
    final affectedRows =
        await (_db.update(_db.orders)..where((tbl) => tbl.id.equals(id))).write(
          OrdersCompanion(
            deletedAt: Value(DateTime.now()),
            syncStatus: const Value(SyncStatus.pendingUpload),
          ),
        );

    final result = affectedRows > 0;

    // Trigger sync after deleting a record
    _triggerSync();

    return result;
  }

  Future<Order?> getById(int id) async {
    final query = _db.select(_db.orders)
      ..where((tbl) => tbl.id.equals(id))
      ..where((tbl) => tbl.deletedAt.isNull()); // Only get non-deleted records

    return query.getSingleOrNull();
  }

  Future<List<Order>> getAll() async {
    final query = _db.select(_db.orders)
      ..where((tbl) => tbl.deletedAt.isNull()) // Only get non-deleted records
      ..orderBy([
        (t) => OrderingTerm(expression: t.date, mode: OrderingMode.desc),
      ]);

    return query.get();
  }

  Future<void> syncData() async {
    await _syncService.syncNow(SyncOperation.full);
  }

  // Helper method to trigger sync with debounce
  void _triggerSync() {
    _syncService.syncData(SyncOperation.upload);
  }

  // Get orders for a specific date range
  Future<List<Order>> getOrdersForDateRange(
    DateTime start,
    DateTime end,
  ) async {
    // Get all orders and filter by date range in memory
    final query = _db.select(_db.orders)
      ..where((tbl) => tbl.deletedAt.isNull())
      ..orderBy([
        (t) => OrderingTerm(expression: t.date, mode: OrderingMode.desc),
      ]);

    final orders = await query.get();

    // Filter by date range
    return orders
        .where(
          (order) =>
              order.date.isAfter(start.subtract(const Duration(days: 1))) &&
              order.date.isBefore(end.add(const Duration(days: 1))),
        )
        .toList();
  }
}
