import 'package:flutter/material.dart';
import 'package:riverpod/riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../../../core/errors/failures.dart';
import '../../../../core/providers/app_settings_provider.dart';
import '../../../../core/providers/global_date_range_provider.dart';
import '../../../../core/services/sync_service.dart';
import '../../../../core/utils/date_helper.dart';
import '../../data/repositories/order_repository_impl.dart';
import '../../domain/entities/order.dart' as domain;
import '../../domain/repositories/order_repository.dart';
import '../../domain/use_cases/calculate_bid_acceptance.dart';
import '../../domain/use_cases/get_order_summary.dart';

part 'order_providers.g.dart';

// Repository provider
@riverpod
OrderRepository orderRepository(Ref ref) {
  final database = ref.watch(databaseProvider);
  final calculateBidAcceptance = CalculateBidAcceptance();
  final syncService = ref.watch(syncServiceProvider);

  return OrderRepositoryImpl(
    database: database,
    calculateBidAcceptance: calculateBidAcceptance,
    syncService: syncService,
  );
}

// Use case provider
@riverpod
GetOrderSummary getOrderSummary(Ref ref) {
  final repository = ref.watch(orderRepositoryProvider);
  return GetOrderSummary(repository);
}

// Provider for the order list
@riverpod
class OrderList extends _$OrderList {
  // All usages of Order below should be domain.Order unless referring to Drift database Order

  @override
  Future<List<domain.Order>> build() async {
    return _fetchOrderList();
  }

  Future<List<domain.Order>> _fetchOrderList() async {
    final getOrderSummary = ref.watch(getOrderSummaryProvider);
    final result = await getOrderSummary.getAll();

    return result.fold((failure) {
      _handleFailure(failure);
      return [];
    }, (orderList) => orderList);
  }

  void _handleFailure(Failure failure) {
    // Log the error or show a notification
    debugPrint('Error fetching orders: ${failure.toString()}');
  }

  Future<void> refreshOrderList() async {
    state = const AsyncValue.loading();
    state = await AsyncValue.guard(() => _fetchOrderList());
  }

  Future<bool> addOrder(domain.Order order) async {
    final repository = ref.read(orderRepositoryProvider);
    final result = await repository.saveOrder(order);

    return result.fold(
      (failure) {
        _handleFailure(failure);
        return false;
      },
      (_) async {
        await refreshOrderList();
        return true;
      },
    );
  }

  Future<bool> updateOrder(domain.Order order) async {
    final repository = ref.read(orderRepositoryProvider);
    final result = await repository.updateOrder(order);

    return result.fold(
      (failure) {
        _handleFailure(failure);
        return false;
      },
      (_) async {
        await refreshOrderList();
        return true;
      },
    );
  }

  Future<bool> deleteOrder(int id) async {
    final repository = ref.read(orderRepositoryProvider);
    final result = await repository.deleteOrder(id);

    return result.fold(
      (failure) {
        _handleFailure(failure);
        return false;
      },
      (_) async {
        await refreshOrderList();
        return true;
      },
    );
  }
}

// Date range selection provider - now using global date range
@Deprecated('Use globalDateRangeProvider instead')
@riverpod
class OrderDateRange extends _$OrderDateRange {
  @override
  AsyncValue<DateTimeRange> build() {
    // Forward to global date range provider
    return ref.watch(globalDateRangeProvider);
  }

  Future<void> setDateRange(DateTimeRange range) async {
    // Forward to global date range provider
    await ref.read(globalDateRangeProvider.notifier).setDateRange(range);
  }
}

// Filtered order list provider
@riverpod
Future<List<domain.Order>> filteredOrderList(Ref ref) async {
  final dateRangeAsync = ref.watch(globalDateRangeProvider);
  final orderList = await ref.watch(orderListProvider.future);

  // No filtering needed if list is empty
  if (orderList.isEmpty) {
    return [];
  }

  // Wait for date range to be available
  if (!dateRangeAsync.hasValue) {
    // Sort by date (newest first) even when no date range is selected
    final sortedList = List<domain.Order>.from(orderList)
      ..sort((a, b) => b.date.compareTo(a.date));
    return sortedList;
  }

  final dateRange = dateRangeAsync.value!;

  // Ensure date range is in UTC for consistent comparison
  final startUtc = DateHelper.ensureUtc(dateRange.start);
  final endUtc = DateHelper.ensureUtc(
    dateRange.end.add(const Duration(days: 1)),
  );

  final filteredList = orderList.where((order) {
    // Ensure order date is in UTC for consistent comparison
    final orderDateUtc = DateHelper.ensureUtc(order.date);

    return orderDateUtc.isAtSameMomentAs(startUtc) ||
        orderDateUtc.isAtSameMomentAs(endUtc) ||
        (orderDateUtc.isAfter(startUtc) && orderDateUtc.isBefore(endUtc));
  }).toList();

  // Sort by date (newest first)
  filteredList.sort((a, b) => b.date.compareTo(a.date));

  return filteredList;
}

// Order summary provider
@riverpod
Future<OrderSummary> orderSummary(Ref ref) async {
  final orderList = await ref.watch(filteredOrderListProvider.future);
  final getOrderSummary = ref.watch(getOrderSummaryProvider);

  final result = getOrderSummary.calculateSummary(orderList);

  return result.fold((failure) {
    debugPrint('Error calculating order summary: ${failure.toString()}');
    return OrderSummary.empty();
  }, (summary) => summary);
}

// Current month points provider (for level calculations)
@riverpod
Future<int> currentMonthPoints(Ref ref) async {
  final now = DateTime.now();
  final getOrderSummary = ref.watch(getOrderSummaryProvider);

  final result = await getOrderSummary.getTotalPointsForMonth(
    now.year,
    now.month,
  );

  return result.fold((failure) {
    debugPrint('Error fetching current month points: ${failure.toString()}');
    return 0;
  }, (points) => points);
}

// Current month bid acceptance provider (for level calculations)
@riverpod
Future<double> currentMonthBidAcceptance(Ref ref) async {
  final orderList = await ref.watch(filteredOrderListProvider.future);

  if (orderList.isEmpty) return 0.0;

  int totalOrdersReceived = 0;
  int totalCbsOrders = 0;
  int totalIncomingOrders = 0;

  for (final order in orderList) {
    totalOrdersReceived += order.orderReceived ?? 0;
    totalCbsOrders += order.cbsOrder;
    totalIncomingOrders += order.incomingOrder ?? 0;
  }

  if (totalIncomingOrders == 0) return 0.0;

  return (totalOrdersReceived + totalCbsOrders) / totalIncomingOrders;
}

// Current month trip completion provider (for level calculations)
@riverpod
Future<double> currentMonthTripCompletion(Ref ref) async {
  final orderList = await ref.watch(filteredOrderListProvider.future);

  if (orderList.isEmpty) return 0.0;

  int totalOrdersReceived = 0;
  int totalOrdersCompleted = 0;

  for (final order in orderList) {
    totalOrdersReceived += order.orderReceived ?? 0;
    totalOrdersCompleted += order.orderCompleted;
  }

  if (totalOrdersReceived == 0) return 0.0;

  return totalOrdersCompleted / totalOrdersReceived;
}
